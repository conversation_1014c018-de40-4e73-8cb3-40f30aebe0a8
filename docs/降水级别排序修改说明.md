# 降水级别排序修改说明

## 修改概述

根据用户需求，对历史个例考试详情中的TS评分详情进行了两项修改：
1. 将降水级别排序调整为从"晴雨"开始
2. 取消"微量降水"在排序中的显示

## 修改的文件

### 1. src/views/user/weather/result.vue

**方法**: `getSortedTSLevels`

**修改内容**:
- 将排序顺序从 `['小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']` 
- 修改为 `['晴雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']`
- 添加了对"微量降水"的过滤，确保它不会在排序结果中显示

**修改前**:
```javascript
// 定义降水级别的显示顺序（取消晴雨，小雨开始）
const levelOrder = ['小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']

// 获取对象中存在的级别
const availableLevels = Object.keys(scoresObj)
```

**修改后**:
```javascript
// 定义降水级别的显示顺序（从晴雨开始）
const levelOrder = ['晴雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']

// 获取对象中存在的级别，过滤掉微量降水
const availableLevels = Object.keys(scoresObj).filter(level => level !== '微量降水')
```

### 2. src/views/weather/exam/result.vue

**方法**: `getSortedPrecipitationLevels`

**修改内容**:
- 同样将排序顺序调整为从"晴雨"开始
- 添加了对"微量降水"的过滤

**修改前**:
```javascript
// 定义降水级别的显示顺序
const levelOrder = ['中雨', '大雨', '暴雨', '大暴雨', '特大暴雨', '小雨']

// 获取对象中存在的级别
const availableLevels = Object.keys(scoresObj)
```

**修改后**:
```javascript
// 定义降水级别的显示顺序（从晴雨开始）
const levelOrder = ['晴雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']

// 获取对象中存在的级别，过滤掉微量降水
const availableLevels = Object.keys(scoresObj).filter(level => level !== '微量降水')
```

## 排序逻辑说明

### 新的排序顺序
1. **晴雨** - 首位显示
2. **小雨** - 第二位
3. **中雨** - 第三位
4. **大雨** - 第四位
5. **暴雨** - 第五位
6. **大暴雨** - 第六位
7. **特大暴雨** - 第七位

### 过滤逻辑
- **微量降水**级别会被完全过滤掉，不会在任何排序结果中显示
- 其他未在预定义顺序中的级别会被添加到排序结果的末尾（但排除微量降水）

### 双重过滤保护
为了确保"微量降水"不会出现在结果中，在两个地方都添加了过滤：

1. **初始过滤**: `Object.keys(scoresObj).filter(level => level !== '微量降水')`
2. **剩余级别过滤**: `availableLevels.filter(level => !levelOrder.includes(level) && level !== '微量降水')`

## 影响范围

### 前端显示
- 历史个例考试详情页面的TS评分详情
- 降水落区评分的各项评分显示
- 基础评分、技能评分、学生TS评分、CMA-MESO TS评分的排序

### 用户体验
- 用户现在会看到从"晴雨"开始的有序排列
- "微量降水"不再显示在评分详情中，避免混淆
- 排序更加符合降水强度的自然顺序

## 测试建议

1. **查看历史考试结果**
   - 打开任意历史个例考试详情
   - 查看TS评分详情部分
   - 确认排序从"晴雨"开始

2. **验证微量降水过滤**
   - 查看包含微量降水数据的考试结果
   - 确认"微量降水"不在显示列表中

3. **检查不同评分类型**
   - 基础评分排序
   - 技能评分排序
   - 学生TS评分排序
   - CMA-MESO TS评分排序

## 注意事项

1. **数据完整性**: 虽然前端不显示"微量降水"，但后端的评分计算逻辑仍然保持不变
2. **向后兼容**: 修改只影响显示顺序，不影响数据存储和计算
3. **性能影响**: 过滤操作对性能影响微乎其微
4. **维护性**: 如果将来需要调整排序或过滤规则，只需修改这两个方法中的配置数组

## 部署说明

这是纯前端修改，只需要：
1. 重新构建前端项目
2. 部署更新后的前端文件
3. 无需重启后端服务
4. 无需数据库变更

修改完成后，用户在查看历史个例考试详情时，TS评分详情将按照新的排序规则显示，微量降水级别将被隐藏。
