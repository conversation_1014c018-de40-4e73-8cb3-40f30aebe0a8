# 恢复硬编码上传路径修改说明

## 修改概述

将天气模块文件上传控制器中的配置文件读取路径恢复为硬编码路径，简化部署和配置管理。

## 修改的文件

### 1. WeatherFileUploadController.java

**文件路径**: `src/main/java/com/yf/exam/modules/weather/controller/WeatherFileUploadController.java`

**主要修改**:

#### 1.1 移除配置类依赖
```java
// 修改前
@Autowired
private WeatherUploadConfig weatherUploadConfig;

// 修改后
// 硬编码的上传路径配置
private static final String BASE_UPLOAD_DIR = "/data/upload/weather/";
private static final String BASE_UPLOAD_URL = "http://localhost:8101/upload/file/weather/";
```

#### 1.2 移除配置类导入
```java
// 移除了这行导入
import com.yf.exam.modules.weather.config.WeatherUploadConfig;
```

#### 1.3 修改MICAPS文件上传方法
```java
// 修改前
String uploadDir = weatherUploadConfig.getMicapsDir();
result.put("url", weatherUploadConfig.getMicapsUrl() + fileName);

// 修改后
String uploadDir = BASE_UPLOAD_DIR + "micaps/";
result.put("url", BASE_UPLOAD_URL + "micaps/" + fileName);
```

#### 1.4 修改实况文件上传方法
```java
// 修改前
String uploadDir = weatherUploadConfig.getObservationDir();
result.put("url", weatherUploadConfig.getObservationUrl() + fileName);

// 修改后
String uploadDir = BASE_UPLOAD_DIR + "observation/";
result.put("url", BASE_UPLOAD_URL + "observation/" + fileName);
```

#### 1.5 修改数据文件上传方法
```java
// 修改前
String uploadDir = weatherUploadConfig.getDataDir();
result.put("url", weatherUploadConfig.getDataUrl() + fileName);

// 修改后
String uploadDir = BASE_UPLOAD_DIR + "data/";
result.put("url", BASE_UPLOAD_URL + "data/" + fileName);
```

#### 1.6 修改降水落区文件上传方法
```java
// 修改前
String uploadDir = weatherUploadConfig.getPrecipitationAreaDir();
result.put("url", weatherUploadConfig.getPrecipitationAreaUrl() + fileName);

// 修改后
String uploadDir = BASE_UPLOAD_DIR + "precipitation-area/";
result.put("url", BASE_UPLOAD_URL + "precipitation-area/" + fileName);
```

## 硬编码路径配置

### 存储路径结构
```
/data/upload/weather/
├── micaps/              # MICAPS文件存储目录
├── observation/         # 实况文件存储目录
├── data/               # 数据文件存储目录
└── precipitation-area/ # 降水落区文件存储目录
```

### 访问URL结构
```
http://localhost:8101/upload/file/weather/
├── micaps/{filename}              # MICAPS文件访问URL
├── observation/{filename}         # 实况文件访问URL
├── data/{filename}               # 数据文件访问URL
└── precipitation-area/{filename} # 降水落区文件访问URL
```

## 保留的文件

以下文件保留但不再使用：
- `src/main/java/com/yf/exam/modules/weather/config/WeatherUploadConfig.java`
- 配置文件中的 `conf.weather` 配置项

这些文件保留是为了避免影响其他可能的依赖，但实际上传功能不再使用这些配置。

## 优势

1. **简化部署**: 不需要配置文件设置，直接使用固定路径
2. **减少依赖**: 移除了对配置类的依赖
3. **提高稳定性**: 避免了配置错误导致的上传失败
4. **便于维护**: 路径直接在代码中可见，便于调试和维护

## 注意事项

1. 确保 `/data/upload/weather/` 目录具有读写权限
2. 如需修改路径，直接修改常量 `BASE_UPLOAD_DIR` 和 `BASE_UPLOAD_URL`
3. 在不同环境部署时，可能需要根据实际情况调整这两个常量的值
4. Web服务器需要配置静态资源映射，确保 `/upload/file/weather/` 路径可以正确访问到物理文件

## 部署建议

### 开发环境
```java
private static final String BASE_UPLOAD_DIR = "D:/exam-upload/weather/";
private static final String BASE_UPLOAD_URL = "http://localhost:8101/upload/file/weather/";
```

### 生产环境
```java
private static final String BASE_UPLOAD_DIR = "/data/upload/weather/";
private static final String BASE_UPLOAD_URL = "https://your-domain.com/upload/file/weather/";
```
