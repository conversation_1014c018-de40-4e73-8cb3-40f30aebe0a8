# 文件路径问题修复说明

## 问题描述

用户遇到了 `java.io.FileNotFoundException: MICAPS文件不存在: weather/precipitation-area/f6b22f1a1783449388be583d2f7ea22e.000` 错误。

## 问题分析

这个问题的根本原因是文件路径不一致：

1. **上传时的路径**：文件被保存到绝对路径 `/data/upload/weather/precipitation-area/`
2. **数据库存储的路径**：存储的是相对路径 `weather/precipitation-area/`
3. **读取时的路径**：系统直接使用相对路径去读取文件，但文件实际在绝对路径中

## 解决方案

### 1. 创建文件路径解析工具类

**文件**: `src/main/java/com/yf/exam/modules/weather/util/WeatherFilePathUtil.java`

**功能**:
- 将相对路径转换为绝对路径
- 支持多种路径查找策略
- 提供静态方法，可以在任何地方使用

**核心方法**:
```java
// 查找文件的多种路径尝试
public static String findFileWithMultiplePaths(String filePath)

// 解析文件路径
public static String resolveFilePath(String filePath)

// 检查文件是否存在
public static boolean fileExists(String filePath)
```

### 2. 修改MICAPS解析器

#### 2.1 修改 MicapsParser.java
- 添加了 `WeatherFilePathUtil` 导入
- 添加了 `@Slf4j` 注解用于日志记录
- 修改 `parseMicapsFile` 方法使用路径解析工具

**修改前**:
```java
public static MicapsData parseMicapsFile(String filePath) throws IOException {
    File file = new File(filePath);
    if (!file.exists()) {
        throw new FileNotFoundException("MICAPS文件不存在: " + filePath);
    }
```

**修改后**:
```java
public static MicapsData parseMicapsFile(String filePath) throws IOException {
    // 使用路径解析工具来查找文件
    String resolvedPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
    if (resolvedPath == null) {
        throw new FileNotFoundException("MICAPS文件不存在: " + filePath);
    }
    
    log.debug("解析MICAPS文件：{} -> {}", filePath, resolvedPath);
    File file = new File(resolvedPath);
```

#### 2.2 修改 MicapsBinaryParser.java
- 添加了 `WeatherFilePathUtil` 导入
- 添加了 `@Slf4j` 注解用于日志记录
- 修改 `parseBinaryMicapsFile` 方法使用路径解析工具

### 3. 创建文件路径解析服务

**文件**: `src/main/java/com/yf/exam/modules/weather/service/WeatherFilePathService.java`

**功能**:
- Spring Bean，可以注入到其他服务中
- 提供更高级的路径解析功能
- 与上传配置集成

### 4. 修改复杂版本的MicapsDataService

**文件**: `src/main/java/com/yf/exam/modules/weather/micaps/MicapsDataService.java`

- 注入了 `WeatherFilePathService`
- 修改 `parseMicapsFile` 方法使用路径解析服务

## 路径解析策略

工具类会按以下顺序尝试查找文件：

1. **解析相对路径为绝对路径**
   - `weather/precipitation-area/file.000` → `/data/upload/weather/precipitation-area/file.000`

2. **尝试原始路径**
   - 如果已经是绝对路径，直接使用

3. **尝试工作目录**
   - `System.getProperty("user.dir") + "/" + filePath`

4. **尝试资源目录**
   - `src/main/resources/` + filePath
   - `src/test/resources/` + filePath

## 硬编码路径配置

为了与上传控制器保持一致，使用以下硬编码路径：

```java
private static final String WEATHER_BASE_DIR = "/data/upload/weather/";
```

**目录结构**:
```
/data/upload/weather/
├── micaps/              # MICAPS文件存储目录
├── observation/         # 实况文件存储目录
├── data/               # 数据文件存储目录
└── precipitation-area/ # 降水落区文件存储目录
```

## 日志记录

添加了详细的日志记录来帮助调试：

```java
log.debug("解析MICAPS文件：{} -> {}", filePath, resolvedPath);
log.debug("文件找到（原始路径）：{}", resolvedPath);
log.warn("文件不存在：{} (解析后路径：{})", filePath, resolvedPath);
log.warn("文件未找到，尝试了多种路径：{}", filePath);
```

## 测试建议

1. **检查日志输出**：查看控制台是否有路径解析的调试信息
2. **验证文件存在**：确认 `/data/upload/weather/precipitation-area/` 目录下确实有相应的文件
3. **测试不同路径**：尝试使用绝对路径和相对路径调用解析方法

## 兼容性

这个修改是向后兼容的：
- 原有的绝对路径仍然可以正常工作
- 新的相对路径会被自动解析为绝对路径
- 不会影响现有的功能

## 部署注意事项

1. 确保 `/data/upload/weather/` 目录存在且有读写权限
2. 如果在不同环境中部署，可能需要调整 `WEATHER_BASE_DIR` 常量
3. 建议在开发环境中启用 DEBUG 日志级别来查看路径解析过程

## 后续优化建议

1. 可以考虑将路径配置移到配置文件中，但保留硬编码作为默认值
2. 可以添加文件缓存机制来提高性能
3. 可以添加文件路径验证和安全检查
