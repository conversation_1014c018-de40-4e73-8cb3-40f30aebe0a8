# 天气模块文件上传路径配置化改造

## 改造概述

将天气模块文件上传控制器中的硬编码路径改为从配置文件读取，提高系统的可配置性和部署灵活性。

## 修改内容

### 1. 新增配置类

**文件**: `src/main/java/com/yf/exam/modules/weather/config/WeatherUploadConfig.java`

- 创建专门的天气模块文件上传配置类
- 使用 `@ConfigurationProperties(prefix = "conf.weather")` 注解
- 提供各种文件类型的目录和URL获取方法

### 2. 修改配置文件

**文件**: `src/main/resources/application-local.yml` 和 `src/main/resources/application-dev.yml`

添加天气模块专用配置：
```yaml
conf:
  # 天气模块文件上传配置
  weather:
    # 天气文件存储基础路径，以/结束
    base-dir: /data/upload/weather/
    # 天气文件访问基础URL
    base-url: http://localhost:8101/upload/file/weather/
```

### 3. 修改控制器

**文件**: `src/main/java/com/yf/exam/modules/weather/controller/WeatherFileUploadController.java`

主要修改：
- 注入 `WeatherUploadConfig` 配置类
- 将所有硬编码路径替换为配置读取
- 修改四个上传方法：
  - `uploadMicapsFile()` - MICAPS文件上传
  - `uploadObservationFile()` - 实况文件上传
  - `uploadDataFile()` - 数据文件上传
  - `uploadPrecipitationAreaFile()` - 降水落区文件上传

## 配置说明

### 配置项说明

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `conf.weather.base-dir` | 天气文件存储基础路径，必须以/结束 | `/data/upload/weather/` |
| `conf.weather.base-url` | 天气文件访问基础URL | `http://localhost:8101/upload/file/weather/` |

### 自动生成的子目录

配置类会自动为不同类型的文件生成子目录：

- **MICAPS文件**: `{base-dir}micaps/`
- **实况文件**: `{base-dir}observation/`
- **数据文件**: `{base-dir}data/`
- **降水落区文件**: `{base-dir}precipitation-area/`

### 访问URL

对应的访问URL也会自动生成：

- **MICAPS文件**: `{base-url}micaps/{filename}`
- **实况文件**: `{base-url}observation/{filename}`
- **数据文件**: `{base-url}data/{filename}`
- **降水落区文件**: `{base-url}precipitation-area/{filename}`

## 部署配置示例

### 开发环境 (application-dev.yml)
```yaml
conf:
  weather:
    base-dir: /Users/<USER>/Documents/work/upload/weather/
    base-url: http://localhost:8201/upload/file/weather/
```

### 生产环境 (application-prod.yml)
```yaml
conf:
  weather:
    base-dir: /data/upload/weather/
    base-url: https://your-domain.com/upload/file/weather/
```

### Windows环境 (application-local.yml)
```yaml
conf:
  weather:
    base-dir: D:/exam-upload/weather/
    base-url: http://localhost:8101/upload/file/weather/
```

## 优势

1. **配置化**: 文件存储路径不再硬编码，可根据环境灵活配置
2. **统一管理**: 所有天气模块的文件上传路径在一个配置类中统一管理
3. **易于部署**: 不同环境可以使用不同的存储路径，无需修改代码
4. **类型安全**: 使用强类型配置类，避免字符串拼接错误
5. **可扩展**: 新增文件类型时只需在配置类中添加对应方法

## 注意事项

1. 确保配置的目录路径具有读写权限
2. 路径配置必须以 `/` 结尾
3. URL配置应与实际的Web服务器配置保持一致
4. 在集群部署时，建议使用共享存储或对象存储服务
