<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小数解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case.pass {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .test-case.fail {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .result {
            font-weight: bold;
        }
        .expected {
            color: #28a745;
        }
        .actual {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>小数解析测试</h1>
    <p>测试修复后的正则表达式是否能正确识别小数格式的降水量</p>
    
    <div id="test-results"></div>
    
    <script>
        // 修复后的正则表达式
        const regex = /^(\d+\.?\d*|\d*\.\d+)\s+\d+$/;
        
        // 智能识别函数
        function mapLevelToString(level) {
            const numLevel = parseFloat(level)
            
            if (numLevel >= 0.1 && numLevel < 10.0) {
                return 'level0' // 小雨：0.1-9.9mm
            } else if (numLevel >= 10.0 && numLevel < 25.0) {
                return 'level10' // 中雨：10-24.9mm
            } else if (numLevel >= 25.0 && numLevel < 50.0) {
                return 'level25' // 大雨：25-49.9mm
            } else if (numLevel >= 50.0 && numLevel < 100.0) {
                return 'level50' // 暴雨：50-99.9mm
            } else if (numLevel >= 100.0 && numLevel < 250.0) {
                return 'level100' // 大暴雨：100-249.9mm
            } else if (numLevel >= 250.0) {
                return 'level250' // 特大暴雨：≥250mm
            } else {
                return `level${level}`
            }
        }

        // 模拟文件解析逻辑
        function parseFileLine(line) {
            if (line.match(regex)) {
                const [firstNum, pointCount] = line.split(/\s+/).map(Number);
                if (pointCount === 1) {
                    // 这是降水量结束标识
                    const precipitationLevel = mapLevelToString(firstNum);
                    return {
                        matched: true,
                        firstNum: firstNum,
                        pointCount: pointCount,
                        precipitationLevel: precipitationLevel
                    };
                }
            }
            return { matched: false };
        }

        // 测试用例
        const testCases = [
            // 小数测试
            { input: "0.1 1", expected: { matched: true, firstNum: 0.1, precipitationLevel: 'level0' }, description: '小数：0.1' },
            { input: "5.5 1", expected: { matched: true, firstNum: 5.5, precipitationLevel: 'level0' }, description: '小数：5.5' },
            { input: "15.2 1", expected: { matched: true, firstNum: 15.2, precipitationLevel: 'level10' }, description: '小数：15.2' },
            { input: "25.8 1", expected: { matched: true, firstNum: 25.8, precipitationLevel: 'level25' }, description: '小数：25.8' },
            
            // 整数测试（向后兼容）
            { input: "10 1", expected: { matched: true, firstNum: 10, precipitationLevel: 'level10' }, description: '整数：10' },
            { input: "25 1", expected: { matched: true, firstNum: 25, precipitationLevel: 'level25' }, description: '整数：25' },
            { input: "100 1", expected: { matched: true, firstNum: 100, precipitationLevel: 'level100' }, description: '整数：100' },
            
            // 坐标点数测试
            { input: "3 55", expected: { matched: true, firstNum: 3, precipitationLevel: 'level3' }, description: '坐标点：3 55' },
            { input: "5 20", expected: { matched: true, firstNum: 5, precipitationLevel: 'level0' }, description: '坐标点：5 20' },
            
            // 边界情况
            { input: "0 1", expected: { matched: true, firstNum: 0, precipitationLevel: 'level0' }, description: '边界：0' },
            { input: ".5 1", expected: { matched: true, firstNum: 0.5, precipitationLevel: 'level0' }, description: '小数：.5' },
            { input: "10. 1", expected: { matched: true, firstNum: 10, precipitationLevel: 'level10' }, description: '小数：10.' },
            
            // 无效格式
            { input: "abc 1", expected: { matched: false }, description: '无效：abc 1' },
            { input: "10", expected: { matched: false }, description: '无效：10（缺少第二个数字）' },
            { input: "10 abc", expected: { matched: false }, description: '无效：10 abc' }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const actual = parseFileLine(testCase.input);
                
                let passed = true;
                let failureReason = '';
                
                if (testCase.expected.matched !== actual.matched) {
                    passed = false;
                    failureReason = `匹配状态不符：期望${testCase.expected.matched}，实际${actual.matched}`;
                } else if (testCase.expected.matched && actual.matched) {
                    if (Math.abs(testCase.expected.firstNum - actual.firstNum) > 0.001) {
                        passed = false;
                        failureReason = `数值不符：期望${testCase.expected.firstNum}，实际${actual.firstNum}`;
                    } else if (testCase.expected.precipitationLevel !== actual.precipitationLevel) {
                        passed = false;
                        failureReason = `等级不符：期望${testCase.expected.precipitationLevel}，实际${actual.precipitationLevel}`;
                    }
                }
                
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
                testDiv.innerHTML = `
                    <div><strong>测试 ${index + 1}:</strong> ${testCase.description}</div>
                    <div><strong>输入:</strong> "${testCase.input}"</div>
                    <div><strong>正则匹配:</strong> ${actual.matched ? '✅' : '❌'}</div>
                    ${actual.matched ? `
                        <div><strong>解析数值:</strong> ${actual.firstNum}</div>
                        <div><strong>识别等级:</strong> ${actual.precipitationLevel}</div>
                    ` : ''}
                    <div class="result">${passed ? '✅ 通过' : '❌ 失败: ' + failureReason}</div>
                `;
                resultsDiv.appendChild(testDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.style.cssText = 'margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;';
            summaryDiv.innerHTML = `
                <h3>测试总结</h3>
                <p><strong>通过:</strong> ${passCount}/${totalCount} (${(passCount/totalCount*100).toFixed(1)}%)</p>
                <p><strong>状态:</strong> ${passCount === totalCount ? '✅ 全部通过' : '❌ 存在失败'}</p>
                <h4>修复内容:</h4>
                <p><strong>修复前正则:</strong> <code>/^\\d+\\s+\\d+$/</code> (只匹配整数)</p>
                <p><strong>修复后正则:</strong> <code>/^(\\d+\\.?\\d*|\\d*\\.\\d+)\\s+\\d+$/</code> (支持小数)</p>
                <h4>支持的格式:</h4>
                <ul>
                    <li>整数: 10 1, 25 1, 100 1</li>
                    <li>小数: 0.1 1, 5.5 1, 15.2 1</li>
                    <li>特殊小数: .5 1, 10. 1</li>
                </ul>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
