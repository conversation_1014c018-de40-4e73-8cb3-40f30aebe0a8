#!/bin/bash

# Ubuntu环境下文件上传目录设置脚本

echo "=== 设置文件上传目录 ==="

# 检查是否以root权限运行
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  检测到以root权限运行，将设置适当的用户权限"
    TARGET_USER=${SUDO_USER:-$USER}
else
    TARGET_USER=$USER
fi

echo "目标用户: $TARGET_USER"

# 1. 创建上传目录
echo "创建上传目录..."
sudo mkdir -p /data/upload/weather/micaps
sudo mkdir -p /data/upload/weather/observation
sudo mkdir -p /data/upload/weather/data
sudo mkdir -p /data/upload/weather/precipitation-area

# 2. 设置目录权限
echo "设置目录权限..."
sudo chown -R $TARGET_USER:$TARGET_USER /data/upload
sudo chmod -R 755 /data/upload

# 3. 检查目录是否创建成功
echo "检查目录结构..."
ls -la /data/upload/weather/

# 4. 测试写入权限
echo "测试写入权限..."
if sudo -u $TARGET_USER touch /data/upload/weather/test.txt 2>/dev/null; then
    echo "✅ 写入权限正常"
    sudo -u $TARGET_USER rm /data/upload/weather/test.txt
else
    echo "❌ 写入权限异常，请检查权限设置"
    echo "尝试修复权限..."
    sudo chmod -R 775 /data/upload
    sudo chown -R $TARGET_USER:$TARGET_USER /data/upload
fi

# 5. 显示最终状态
echo ""
echo "=== 最终状态检查 ==="
echo "目录权限:"
ls -ld /data/upload/weather/*/

echo ""
echo "磁盘空间:"
df -h /data/upload

echo ""
echo "=== 目录设置完成 ==="
echo "如果仍有问题，请检查:"
echo "1. Java应用是否以 $TARGET_USER 用户运行"
echo "2. SELinux是否启用并阻止访问"
echo "3. 防火墙设置是否正确"
