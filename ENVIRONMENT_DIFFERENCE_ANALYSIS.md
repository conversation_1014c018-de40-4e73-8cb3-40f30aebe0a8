# Windows vs Ubuntu 环境差异分析指南

## 🔍 问题现象
- **开发环境 (Windows 11)**：`CustomJacksonTypeHandler` 正常工作，能获取JSON数据
- **生产环境 (Ubuntu 22)**：`CustomJacksonTypeHandler` 返回 null，但能获取题目表的JSON数据
- **关键线索**：同样的 TypeHandler，对不同表的处理结果不同

## 🎯 可能的根本原因

### 1. **字符编码差异**
**Windows vs Ubuntu 的默认字符编码可能不同**
- Windows 通常使用 GBK 或 UTF-8
- Ubuntu 通常使用 UTF-8
- 数据库连接的字符编码设置可能不一致

### 2. **数据库驱动版本差异**
**不同环境可能使用了不同版本的 MySQL 驱动**
- 驱动版本对字符编码的处理方式可能不同
- 某些版本的驱动对 JSON 字段的处理有 bug

### 3. **数据存储时的编码问题**
**考生答案和题目答案可能使用了不同的编码存储**
- 考生答案可能在前端提交时使用了特定编码
- 题目答案可能在后台导入时使用了不同编码

### 4. **MyBatis 配置差异**
**不同环境的 MyBatis 配置可能不同**
- TypeHandler 的注册方式
- 数据库连接池的配置
- 字符编码相关的配置

## 🛠️ 诊断步骤

### 第一步：基础环境对比
```bash
# 在生产环境执行
GET /exam/api/weather/diagnostic/encoding-test/{answerId}
```

这将输出：
- 操作系统信息
- Java 版本和字符编码设置
- 数据库连接属性
- MySQL 字符集配置

### 第二步：字段处理对比
```bash
# 对比考生答案表和题目表的字段处理
GET /exam/api/weather/diagnostic/compare-json/{answerId}
```

重点关注：
- 两个表的字段类型是否相同
- JSON 解析是否都成功
- 字节长度是否一致

### 第三步：原始数据检查
直接在数据库中执行：
```sql
-- 检查考生答案的原始数据
SELECT 
    id,
    HEX(precipitation_answer) as hex_data,
    LENGTH(precipitation_answer) as data_length,
    CHAR_LENGTH(precipitation_answer) as char_length,
    precipitation_answer
FROM el_weather_history_exam_answer 
WHERE id = 'YOUR_ANSWER_ID';

-- 检查题目的原始数据
SELECT 
    id,
    HEX(scenario_data) as hex_data,
    LENGTH(scenario_data) as data_length,
    CHAR_LENGTH(scenario_data) as char_length,
    LEFT(scenario_data, 200) as data_preview
FROM el_qu 
WHERE id = 'YOUR_QUESTION_ID';
```

### 第四步：字符编码测试
```sql
-- 检查数据库字符集设置
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 检查表的字符集
SHOW CREATE TABLE el_weather_history_exam_answer;
SHOW CREATE TABLE el_qu;
```

## 🔧 可能的解决方案

### 方案1：统一字符编码配置
在 `application-server.yml` 中添加：
```yaml
spring:
  datasource:
    url: *****************************************************************************************************************
```

### 方案2：修复数据存储编码
如果发现数据存储时编码有问题，可能需要：
```sql
-- 修复表字符集
ALTER TABLE el_weather_history_exam_answer CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 方案3：增强 TypeHandler
创建一个更健壮的 TypeHandler：
```java
@Override
public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
    String json = rs.getString(columnName);
    if (json == null || json.trim().isEmpty()) {
        return null;
    }
    
    // 尝试不同的字符编码
    String[] encodings = {"UTF-8", "GBK", "ISO-8859-1"};
    for (String encoding : encodings) {
        try {
            byte[] bytes = json.getBytes("ISO-8859-1");
            String decoded = new String(bytes, encoding);
            return objectMapper.readValue(decoded, Map.class);
        } catch (Exception e) {
            // 继续尝试下一个编码
        }
    }
    
    // 如果都失败了，尝试原始字符串
    try {
        return objectMapper.readValue(json, Map.class);
    } catch (Exception e) {
        log.error("所有编码尝试都失败了", e);
        return null;
    }
}
```

### 方案4：数据库连接池配置
检查 Druid 连接池配置：
```yaml
spring:
  datasource:
    druid:
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000;characterEncoding=utf8;useUnicode=true
```

## 📊 预期的诊断结果

### 正常情况下应该看到：
```log
=== 系统环境测试 ===
操作系统: Linux
默认字符编码: UTF-8
=== MySQL字符集设置 ===
character_set_database: utf8mb4
character_set_connection: utf8mb4
character_set_results: utf8mb4
=== 字符编码处理测试 ===
✅ JSON解析成功
```

### 问题情况下可能看到：
```log
=== 系统环境测试 ===
操作系统: Linux
默认字符编码: UTF-8  # 与Windows不同
=== MySQL字符集设置 ===
character_set_connection: latin1  # 可能的问题
=== 对比不同表字段处理 ===
⚠️ 发现类型差异！这可能是问题的根源
考生答案字段类型: String
题目字段类型: LinkedHashMap
```

## 🎯 下一步行动

1. **立即执行诊断接口**，收集详细信息
2. **对比开发环境和生产环境**的诊断结果
3. **根据诊断结果**选择对应的解决方案
4. **验证修复效果**

## 📞 支持信息

如果诊断后仍无法解决，请提供：
1. 完整的诊断日志输出
2. 开发环境和生产环境的对比结果
3. 数据库表结构和字符集信息
4. 应用配置文件的差异
