# 站点得分详情功能说明

## 功能概述

在前端考试结果页面的"详细评分信息"部分，新增了"查看站点得分详情"按钮，点击后弹出详细的站点评分信息列表。

## 实现的功能

### 1. 站点详情按钮
- 位置：降水落区评分详情下方
- 样式：主色调按钮，带图标
- 加载状态：支持loading状态显示

### 2. 站点详情弹窗
- **弹窗标题**：站点得分详情
- **弹窗大小**：90%宽度，适应内容高度
- **关闭方式**：点击关闭按钮或ESC键

### 3. 统计摘要
显示以下统计信息：
- 总站点数
- 有降水站点数
- 预报正确站点数  
- 正确率百分比

### 4. 筛选功能
支持按以下条件筛选站点：
- **实况等级**：无雨、小雨、中雨、大雨、暴雨、大暴雨、微量降水
- **预报结果**：正确A、空报B、漏报C、正确D、错误B
- **站点ID**：支持模糊搜索

### 5. 站点详情表格
显示每个站点的详细信息：

| 列名 | 说明 | 示例 |
|------|------|------|
| 站点ID | 气象站点编号 | 54511 |
| 位置 | 经纬度坐标 | 116.28°E<br>39.93°N |
| 实况 | 实际降水量和等级 | 0.2mm<br>小雨 |
| 考生预报 | 考生预报降水量和等级 | 5.0mm<br>小雨 |
| MESO预报 | CMA-MESO预报降水量和等级 | 15.0mm<br>中雨 |
| 晴雨评分 | 晴雨TS评分结果和贡献 | 正确A<br>正确+1 |
| 量级评分 | 量级TS评分结果和贡献 | 正确A<br>正确+1 |

### 6. 导出功能
- 支持导出当前筛选结果为CSV文件
- 文件名格式：`站点详情_考试ID_时间戳.csv`
- 包含所有站点详细信息

## 数据来源

### 接口调用
```javascript
// 调用考试结果详情接口
const response = await getWeatherExamResult({ examId: this.examId })
```

### 数据路径
```javascript
// 站点详情数据位置
response.data.gradingDetails.precipitationScoringDetails.stationDetails
```

### 数据结构
```javascript
{
  stationId: 54511,
  longitude: 116.28,
  latitude: 39.93,
  actualPrecipitation: 0.2,
  actualLevel: "小雨",
  studentForecastPrecipitation: 5.0,
  studentForecastLevel: "小雨",
  cmaMesoForecastPrecipitation: 15.0,
  cmaMesoForecastLevel: "中雨",
  rainNoRainDetail: {
    studentResultType: "正确A",
    studentContribution: "正确+1",
    cmaMesoResultType: "正确A",
    cmaMesoContribution: "正确+1"
  },
  levelDetail: {
    level: "小雨",
    studentResultType: "正确A",
    studentContribution: "正确+1",
    cmaMesoResultType: "错误B",
    cmaMesoContribution: "错误+1",
    specialRuleNote: "微量降水特殊规则：预报为微量降水、小雨或无雨都算正确"
  }
}
```

## 样式特点

### 1. 按钮样式
- 居中显示
- 淡蓝色背景
- 虚线边框
- hover效果

### 2. 弹窗样式
- 渐变色标题栏
- 圆角设计
- 响应式布局

### 3. 表格样式
- 斑马纹显示
- 边框分隔
- 固定高度滚动
- 标签颜色区分

### 4. 标签颜色
- **等级标签**：无雨(默认)、小雨(info)、中雨(warning)、大雨(danger)等
- **结果标签**：正确(success)、空报(warning)、漏报(danger)、错误(danger)

## 使用说明

1. 进入考试结果页面
2. 滚动到"详细评分信息"部分
3. 在降水落区评分详情下方点击"查看站点得分详情"按钮
4. 在弹窗中查看站点详细信息
5. 可使用筛选功能查找特定站点
6. 点击"导出CSV"按钮可导出当前数据
7. 点击"关闭"按钮关闭弹窗

## 错误处理

- 如果没有评分数据，显示警告提示
- 如果获取数据失败，显示错误提示
- 如果没有站点数据，显示空状态页面
- 导出时如果没有数据，显示警告提示

## 技术实现

### 前端技术
- Vue.js 2.x
- Element UI
- CSS3 渐变和动画

### 数据处理
- 异步数据加载
- 实时筛选
- CSV导出
- 错误边界处理

### 响应式设计
- 移动端适配
- 弹窗自适应
- 表格横向滚动
