# 降水评分数据不一致问题调试指南

## 问题描述

接口返回的数据中，stationDetails中actualLevel为"中雨"的站点有6个，但界面显示"漏报：8个"，数据不一致。

## 调试步骤

### 1. 检查完整的API响应数据

在浏览器开发者工具中：

```javascript
// 1. 打开Network标签，找到 /exam/api/weather/exam/result/detail 请求
// 2. 查看Response，找到 gradingDetails.precipitationScoringDetails
// 3. 运行以下代码分析数据：

const data = response.data.gradingDetails.precipitationScoringDetails;

// 统计stationDetails中中雨站点
const moderateRainStations = data.stationDetails.filter(station => 
    station.actualLevel === '中雨'
);
console.log('stationDetails中中雨站点数量:', moderateRainStations.length);

// 统计中雨站点中预报为无雨的数量（应该是漏报）
const missedModerateRain = moderateRainStations.filter(station => 
    station.studentForecastLevel === '无雨'
);
console.log('中雨站点中预报为无雨的数量:', missedModerateRain.length);

// 查看中雨TS评分详情
const moderateRainTSDetail = data.levelTSDetails.find(detail => 
    detail.level === '中雨'
);
console.log('中雨TS评分详情:', moderateRainTSDetail);

if (moderateRainTSDetail && moderateRainTSDetail.studentTSStats) {
    console.log('中雨TS统计 - 漏报数量:', moderateRainTSDetail.studentTSStats.missedForecast);
    console.log('中雨TS统计 - 正确数量:', moderateRainTSDetail.studentTSStats.correctForecast);
    console.log('中雨TS统计 - 错误数量:', moderateRainTSDetail.studentTSStats.wrongForecast);
}
```

### 2. 检查区域筛选参数

```javascript
// 检查是否使用了区域筛选
console.log('总站点数:', data.totalStations);
console.log('stationDetails数量:', data.stationDetails.length);

// 如果两个数量不一致，说明可能有区域筛选或数据处理
if (data.totalStations !== data.stationDetails.length) {
    console.log('警告：总站点数与stationDetails数量不一致，可能存在数据筛选');
}
```

### 3. 详细分析中雨站点数据

```javascript
// 详细分析每个中雨站点的预报情况
moderateRainStations.forEach((station, index) => {
    console.log(`中雨站点${index + 1}:`, {
        stationId: station.stationId,
        actualLevel: station.actualLevel,
        studentForecastLevel: station.studentForecastLevel,
        cmaMesoForecastLevel: station.cmaMesoForecastLevel,
        studentResultType: station.levelDetail?.studentResultType,
        location: `${station.longitude}, ${station.latitude}`
    });
});
```

### 4. 检查前端显示逻辑

在Vue组件中检查是否有数据筛选：

```javascript
// 在 src/views/user/weather/result.vue 中添加调试代码
mounted() {
    // 原有代码...
    
    // 添加调试代码
    this.$nextTick(() => {
        if (this.precipitationAreaDetails && this.precipitationAreaDetails.stationDetails) {
            const allStations = this.precipitationAreaDetails.stationDetails;
            const moderateRainStations = allStations.filter(s => s.actualLevel === '中雨');
            
            console.log('前端接收到的中雨站点数量:', moderateRainStations.length);
            console.log('前端显示的漏报数量:', this.getModerateRainMissedCount());
            
            // 检查是否有筛选逻辑
            if (this.filteredStationList) {
                const filteredModerateRain = this.filteredStationList.filter(s => s.actualLevel === '中雨');
                console.log('筛选后的中雨站点数量:', filteredModerateRain.length);
            }
        }
    });
}
```

## 可能的原因分析

### 1. 区域筛选导致的数据不一致

如果使用了区域筛选（regionCode参数），`filterStationsByRegion`方法会：
- 获取区域内所有站点
- 为缺失实况数据的站点创建降水量为0的数据
- 这可能导致实际参与评分的站点数量比原始实况数据多

### 2. 数据处理过程中的站点补充

在数据处理过程中，可能会：
- 补充缺失的站点数据
- 插值生成预报数据
- 这些操作可能影响最终的站点数量统计

### 3. 前端显示逻辑问题

前端可能存在：
- 数据筛选逻辑
- 分页显示限制
- 数据路径获取错误

## 修复建议

### 1. 添加详细日志

在`PrecipitationAreaScoringService.java`中添加调试日志：

```java
// 在calculateLevelTSWithDetails方法中添加
log.info("计算{}TS评分，参与评分的站点总数：{}", level, actualData.size());

int actualLevelCount = 0;
int missedCount = 0;

for (int i = 0; i < actualData.size(); i++) {
    StationPrecipitationData actual = actualData.get(i);
    StationPrecipitationData forecast = forecastData.get(i);
    
    if (level.equals(actual.getActualLevel())) {
        actualLevelCount++;
        if ("无雨".equals(forecast.getForecastLevel())) {
            missedCount++;
            log.debug("站点{}：实况{}，预报{}，计为漏报", 
                actual.getStationId(), actual.getActualLevel(), forecast.getForecastLevel());
        }
    }
}

log.info("{}量级：实况站点数{}，漏报数{}", level, actualLevelCount, missedCount);
```

### 2. 数据一致性检查

添加数据一致性检查方法：

```java
private void validateDataConsistency(PrecipitationScoringResult result) {
    if (result.getStationDetails() != null && result.getLevelTSDetails() != null) {
        for (LevelTSScoringDetail levelDetail : result.getLevelTSDetails()) {
            String level = levelDetail.getLevel();
            if (!"晴雨".equals(level)) {
                long stationDetailsCount = result.getStationDetails().stream()
                    .filter(s -> level.equals(s.getActualLevel()))
                    .count();
                
                int tsStatsTotal = levelDetail.getStudentTSStats().getCorrectForecast() +
                                 levelDetail.getStudentTSStats().getWrongForecast() +
                                 levelDetail.getStudentTSStats().getMissedForecast();
                
                if (stationDetailsCount != tsStatsTotal) {
                    log.warn("数据不一致：{}量级 stationDetails中有{}个站点，但TS统计显示{}个站点参与评分",
                        level, stationDetailsCount, tsStatsTotal);
                }
            }
        }
    }
}
```

### 3. 前端数据验证

在前端添加数据验证逻辑：

```javascript
// 验证数据一致性
validatePrecipitationData() {
    if (!this.precipitationAreaDetails) return;
    
    const { stationDetails, levelTSDetails } = this.precipitationAreaDetails;
    
    if (stationDetails && levelTSDetails) {
        levelTSDetails.forEach(levelDetail => {
            if (levelDetail.level !== '晴雨') {
                const stationCount = stationDetails.filter(s => 
                    s.actualLevel === levelDetail.level
                ).length;
                
                const tsTotal = (levelDetail.studentTSStats?.correctForecast || 0) +
                               (levelDetail.studentTSStats?.wrongForecast || 0) +
                               (levelDetail.studentTSStats?.missedForecast || 0);
                
                if (stationCount !== tsTotal) {
                    console.warn(`数据不一致：${levelDetail.level}量级 stationDetails中有${stationCount}个站点，但TS统计显示${tsTotal}个站点`);
                }
            }
        });
    }
}
```

## 预期结果

通过以上调试步骤，应该能够：
1. 确定数据不一致的具体原因
2. 找到漏报数量计算错误的根源
3. 修复数据统计逻辑
4. 确保前后端数据一致性
