# TS评分计算公式修复说明

## 问题描述

在历史个例考试结果详情中，发现三个不同的得分数值不一致：

1. **TS评分**：0.874
2. **计算公式结果**：0.566  
3. **总得分**：0.612

## 问题原因

经过代码检查，发现晴雨TS评分的计算存在**公式不一致**的问题：

### 错误的计算公式

**主要TS评分计算**（`calculateRainNoRainTSWithDetails` 方法第1130行）：
```java
// 错误公式
double tsScore = total > 0 ? (double)(A + D) / total : 0.0;
// 即：TS = (A + D) / (A + B + C + D)
```

**详细记录生成**（`generateMesoRainNoRainDetails` 和 `generateStudentRainNoRainDetails` 方法）：
```java
// 也是错误公式
double tsScore = total > 0 ? (double)(correctA + correctD) / total : 0.0;
// 即：TS = (correctA + correctD) / (correctA + correctD + wrongB + missedC)
```

### 标准TS评分公式

根据气象学标准，**TS评分（Threat Score）**的正确公式应该是：

```
TS = A / (A + B + C)
```

其中：
- **A**：正确预报有雨（Hit）
- **B**：空报（False Alarm）
- **C**：漏报（Miss）
- **D**：正确预报无雨（Correct Negative）

**注意**：TS评分**不包括D（正确预报无雨）**，因为TS评分主要评估对降水事件的预报能力。

## 修复方案

### 1. 修复主要TS评分计算

**文件**：`PrecipitationAreaScoringService.java`  
**方法**：`calculateRainNoRainTSWithDetails`  
**位置**：第1129-1132行

```java
// 修复前
int total = A + B + C + D;
double tsScore = total > 0 ? (double)(A + D) / total : 0.0;

// 修复后
int total = A + B + C + D;
// 标准TS评分公式：TS = A / (A + B + C)
// 对于晴雨TS评分，只考虑正确预报有雨的情况
double tsScore = (A + B + C) > 0 ? (double)A / (A + B + C) : 0.0;
```

### 2. 修复MESO晴雨TS评分详情

**文件**：`PrecipitationAreaScoringService.java`  
**方法**：`generateMesoRainNoRainDetails`  
**位置**：第1462-1474行

```java
// 修复前
int total = correctA + correctD + wrongB + missedC;
double tsScore = total > 0 ? (double)(correctA + correctD) / total : 0.0;
details.put("formula", String.format("TS = (%d + %d) / (%d + %d + %d + %d) = %.3f",
    correctA, correctD, correctA, correctD, wrongB, missedC, tsScore));

// 修复后
int total = correctA + correctD + wrongB + missedC;
// 标准TS评分公式：TS = correctA / (correctA + wrongB + missedC)
// 对于晴雨TS评分，只考虑正确预报有雨的情况
double tsScore = (correctA + wrongB + missedC) > 0 ? (double)correctA / (correctA + wrongB + missedC) : 0.0;
details.put("formula", String.format("TS = %d / (%d + %d + %d) = %.3f",
    correctA, correctA, wrongB, missedC, tsScore));
```

### 3. 修复考生晴雨TS评分详情

**文件**：`PrecipitationAreaScoringService.java`  
**方法**：`generateStudentRainNoRainDetails`  
**位置**：第1611-1623行

```java
// 修复前
int total = correctA + correctD + wrongB + missedC;
double tsScore = total > 0 ? (double)(correctA + correctD) / total : 0.0;
details.put("formula", String.format("TS = (%d + %d) / (%d + %d + %d + %d) = %.3f",
    correctA, correctD, correctA, correctD, wrongB, missedC, tsScore));

// 修复后
int total = correctA + correctD + wrongB + missedC;
// 标准TS评分公式：TS = correctA / (correctA + wrongB + missedC)
// 对于晴雨TS评分，只考虑正确预报有雨的情况
double tsScore = (correctA + wrongB + missedC) > 0 ? (double)correctA / (correctA + wrongB + missedC) : 0.0;
details.put("formula", String.format("TS = %d / (%d + %d + %d) = %.3f",
    correctA, correctA, wrongB, missedC, tsScore));
```

## 修复后的效果

修复后，三个得分将保持一致：

1. **TS评分**：使用标准公式 `A / (A + B + C)` 计算
2. **计算公式结果**：显示相同的标准公式和结果
3. **总得分**：基于正确的TS评分计算技能评分

## 验证方法

使用您提供的数据验证：
- 正确预报有雨：20个
- 正确预报无雨：40个  
- 空报：15个
- 漏报：31个

**修复前的错误计算**：
```
TS = (20 + 40) / (20 + 40 + 15 + 31) = 60 / 106 = 0.566
```

**修复后的正确计算**：
```
TS = 20 / (20 + 15 + 31) = 20 / 66 = 0.303
```

现在所有相关的TS评分计算都将使用统一的标准公式，确保数据的一致性和准确性。

## 影响范围

这个修复影响以下功能：
- 晴雨TS评分的计算
- MESO晴雨TS评分详情的显示
- 考生晴雨TS评分详情的显示
- 基于TS评分的技能评分计算
- 最终评分的计算

**注意**：其他量级（小雨、中雨等）的TS评分计算本来就是正确的，不受此次修复影响。
