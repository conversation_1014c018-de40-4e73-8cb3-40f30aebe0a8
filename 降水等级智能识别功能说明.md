# 降水等级智能识别功能说明

## 功能概述

将降水落区文件解析从固定数字映射改为智能范围识别，使系统能够根据降水量数值自动分类到对应的降水等级。

## 修改内容

### 1. 前端智能识别逻辑

**文件**: `exam-vue/src/components/weather/PrecipitationDrawing.vue`

**修改前** (固定映射):
```javascript
mapLevelToString(level) {
  const levelMap = {
    0: 'level0',     // 只有0映射为小雨
    10: 'level10',   // 只有10映射为中雨
    25: 'level25',   // 只有25映射为大雨
    // ...
  }
  return levelMap[level] || `level${level}`
}
```

**修改后** (智能识别):
```javascript
mapLevelToString(level) {
  const numLevel = parseFloat(level)
  
  if (numLevel >= 0.1 && numLevel < 10.0) {
    return 'level0'    // 小雨：0.1-9.9mm
  } else if (numLevel >= 10.0 && numLevel < 25.0) {
    return 'level10'   // 中雨：10-24.9mm
  } else if (numLevel >= 25.0 && numLevel < 50.0) {
    return 'level25'   // 大雨：25-49.9mm
  } else if (numLevel >= 50.0 && numLevel < 100.0) {
    return 'level50'   // 暴雨：50-99.9mm
  } else if (numLevel >= 100.0 && numLevel < 250.0) {
    return 'level100'  // 大暴雨：100-249.9mm
  } else if (numLevel >= 250.0) {
    return 'level250'  // 特大暴雨：≥250mm
  } else {
    return `level${level}` // 其他情况保持原值
  }
}
```

### 2. 识别规则详解

| 降水量范围 | 等级标识 | 降水等级 | 说明 |
|-----------|---------|---------|------|
| 0.1-9.9mm | level0 | 小雨 | 包含所有小雨范围的数值 |
| 10.0-24.9mm | level10 | 中雨 | 包含所有中雨范围的数值 |
| 25.0-49.9mm | level25 | 大雨 | 包含所有大雨范围的数值 |
| 50.0-99.9mm | level50 | 暴雨 | 包含所有暴雨范围的数值 |
| 100.0-249.9mm | level100 | 大暴雨 | 包含所有大暴雨范围的数值 |
| ≥250.0mm | level250 | 特大暴雨 | 极端降水情况 |
| <0.1mm | level{原值} | 其他 | 保持原始标识 |

## 功能优势

### 1. 更加灵活
- **修改前**: 只能识别固定的几个数值 (0, 10, 25, 50, 100)
- **修改后**: 可以识别任意在范围内的数值 (0.1, 5.5, 15.2, 35.8等)

### 2. 更符合实际
- 实际降水文件中的数值往往不是整数
- 可以处理 1.5mm、8.7mm、23.4mm 等真实降水量值

### 3. 向后兼容
- 原有的固定数值 (10, 25, 50, 100) 仍然能正确识别
- 不会影响现有的文件格式

## 使用示例

### 文件内容示例
```
CLOSED_CONTOURS: 1
3 4
   116.0    40.0     0.000
   117.0    40.0     0.000
   117.0    41.0     0.000
   116.0    41.0     0.000
5.5 1                          ← 5.5mm，智能识别为小雨
   116.5    40.5     0.000

3 4
   117.0    40.0     0.000
   118.0    40.0     0.000
   118.0    41.0     0.000
   117.0    41.0     0.000
15.2 1                         ← 15.2mm，智能识别为中雨
   117.5    40.5     0.000
```

### 识别结果
- `5.5` → `level0` (小雨)
- `15.2` → `level10` (中雨)
- `35.8` → `level25` (大雨)
- `75.3` → `level50` (暴雨)
- `150.6` → `level100` (大暴雨)
- `300.0` → `level250` (特大暴雨)

## 测试验证

### 边界值测试
- ✅ `0.1` → `level0` (小雨下限)
- ✅ `9.9` → `level0` (小雨上限)
- ✅ `10.0` → `level10` (中雨下限)
- ✅ `24.9` → `level10` (中雨上限)
- ✅ `25.0` → `level25` (大雨下限)

### 实际数值测试
- ✅ `2.3` → `level0` (小雨)
- ✅ `18.7` → `level10` (中雨)
- ✅ `42.1` → `level25` (大雨)
- ✅ `88.9` → `level50` (暴雨)
- ✅ `156.4` → `level100` (大暴雨)

### 特殊情况测试
- ✅ `0.0` → `level0.0` (无降水，保持原值)
- ✅ `0.05` → `level0.05` (微量，保持原值)
- ✅ 字符串数字正确解析
- ✅ 无效输入处理

## 部署影响

### 1. 用户体验提升
- 用户不再需要使用固定的数值
- 可以使用更精确的降水量数值
- 文件制作更加灵活

### 2. 数据处理改进
- 支持更多样化的数据源
- 提高数据解析的准确性
- 减少因数值不匹配导致的解析失败

### 3. 兼容性保证
- 现有文件格式完全兼容
- 不影响已保存的考生答案
- 后端评分逻辑无需修改

## 注意事项

### 1. 数值精度
- 系统使用 `parseFloat()` 解析数值
- 支持小数点后多位精度
- 建议使用1位小数精度以提高可读性

### 2. 边界处理
- 边界值按照"左闭右开"原则处理
- 例如：10.0属于中雨，不属于小雨

### 3. 异常处理
- 无效数值会保持原始标识
- 不会导致系统崩溃或数据丢失

## 总结

智能识别功能大大提升了降水落区文件解析的灵活性和准确性，使系统能够处理更多样化的真实降水数据，同时保持了完全的向后兼容性。

**主要改进**:
- ✅ 从固定映射改为范围识别
- ✅ 支持任意精度的降水量数值
- ✅ 提高了数据处理的准确性
- ✅ 保持完全向后兼容
- ✅ 用户体验显著提升
