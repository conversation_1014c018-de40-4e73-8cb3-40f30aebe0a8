# 微量降水评分规则修复总结

## 🎯 修复目标

根据用户需求，修复降水落区评分中的微量降水处理规则：

1. **小雨量级评分**：实况如果是微量降水，在小雨中算正确
2. **晴雨评分**：实况是微量，考生和MESO报无雨和小雨都算正确  
3. **TS评分保存**：需要将考生的TS评分，MESO的TS评分都计算保存

## ✅ 已完成的修复

### 1. 修复TS评分计算和保存逻辑

**文件**: `src/main/java/com/yf/exam/modules/weather/scoring/service/PrecipitationAreaScoringService.java`

**问题**: 原来只计算考生的TS评分，CMA-MESO的TS评分没有被正确收集和保存

**修复**:
```java
// 修复前：CMA-MESO评分没有收集详细信息
Map<String, Double> cmaMesoTSScores = calculateAllLevelTSWithDetails(cmaMesoData, actualData, new ArrayList<>(), false);

// 修复后：两者的TS评分都被正确计算和保存
Map<String, Double> studentTSScores = calculateAllLevelTSWithDetails(studentData, actualData, levelTSDetails, true, "student");
Map<String, Double> cmaMesoTSScores = calculateAllLevelTSWithDetails(cmaMesoData, actualData, levelTSDetails, true, "cmaMeso");
```

### 2. 修复晴雨评分中的微量降水处理

**修复内容**:
- 实况为微量降水时，预报无雨、小雨或微量降水都算正确
- 预报为中雨及以上才算错误

**实现逻辑**:
```java
// 微量降水特殊处理：实况为微量降水时，预报无雨或小雨都算正确
if ("微量降水".equals(actualLevel)) {
    if ("无雨".equals(forecastLevel) || "小雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
        A++; // 正确预报有降水（微量降水特殊规则）
        // 统计到对应的TS统计中
    } else {
        B++; // 空报（预报为中雨及以上）
        // 统计到对应的TS统计中
    }
}
```

### 3. 修复小雨量级评分中的微量降水处理

**修复内容**:
- 实况为微量降水时，在小雨量级评分中算正确
- 预报为微量降水、小雨或无雨都算作小雨的正确预报

**实现逻辑**:
```java
// 处理实况为微量降水的特殊情况
else if ("微量降水".equals(actualLevel)) {
    // 微量降水特殊规则：在小雨量级评分中，预报为微量降水、小雨或无雨都算正确
    if ("小雨".equals(level)) {
        if ("微量降水".equals(forecastLevel) || "小雨".equals(forecastLevel) || "无雨".equals(forecastLevel)) {
            A++; // 算作小雨的正确预报
            // 统计到对应的TS统计中
        } else {
            B++; // 预报为其他量级（中雨及以上）
            // 统计到对应的TS统计中
        }
    }
}
```

### 4. 修复站点详情中的结果类型显示

**修复内容**:
- 晴雨预报结果类型正确显示微量降水的特殊规则
- 量级预报结果类型正确处理微量降水情况

**实现逻辑**:
```java
// 获取晴雨预报结果类型 - 微量降水特殊处理
if ("微量降水".equals(actualLevel)) {
    if ("无雨".equals(forecastLevel) || "小雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
        return "正确A";
    } else {
        return "空报B"; // 预报为中雨及以上
    }
}
```

### 5. 增强TS评分统计功能

**新增功能**:
- 同时统计考生和CMA-MESO的TS评分详情
- 每个量级的TS统计都包含两者的数据
- 支持按评估者类型（student/cmaMeso）分别统计

**数据结构**:
```java
LevelTSScoringDetail {
    level: "小雨",
    studentTSStats: {
        correct: 15,    // 考生正确预报数
        wrong: 3,       // 考生错误预报数  
        missed: 2,      // 考生漏报数
        tsScore: 0.75   // 考生TS得分
    },
    cmaMesoTSStats: {
        correct: 12,    // CMA-MESO正确预报数
        wrong: 5,       // CMA-MESO错误预报数
        missed: 3,      // CMA-MESO漏报数
        tsScore: 0.60   // CMA-MESO TS得分
    },
    specialRuleNote: "包含微量降水特殊规则：实况为微量降水时，预报小雨或无雨都算正确"
}
```

## 🔄 评分规则总结

### 1. 微量降水识别
- 降水量为 0.001mm 时识别为"微量降水"
- 在MICAPS第三类数据中，"T"值被解析为 0.001

### 2. 晴雨评分规则
| 实况 | 预报 | 结果 | 说明 |
|------|------|------|------|
| 微量降水 | 无雨 | 正确A | 特殊规则 |
| 微量降水 | 小雨 | 正确A | 特殊规则 |
| 微量降水 | 微量降水 | 正确A | 特殊规则 |
| 微量降水 | 中雨及以上 | 空报B | 预报过大 |
| 其他 | 其他 | 常规规则 | 按原有逻辑 |

### 3. 小雨量级评分规则
| 实况 | 预报 | 结果 | 说明 |
|------|------|------|------|
| 小雨 | 小雨 | 正确A | 常规规则 |
| 微量降水 | 无雨 | 正确A | 特殊规则：算作小雨正确 |
| 微量降水 | 小雨 | 正确A | 特殊规则：算作小雨正确 |
| 微量降水 | 微量降水 | 正确A | 特殊规则：算作小雨正确 |
| 微量降水 | 中雨及以上 | 错误B | 预报过大 |

### 4. 其他量级评分规则
- 中雨、大雨、暴雨、大暴雨：按原有规则，不受微量降水影响
- 微量降水不单独作为一个评分量级

## 📊 TS评分计算公式

### 1. 晴雨TS评分
```
晴雨TS = (A + D) / (A + B + C + D)
其中：
- A：正确预报有降水（包括微量降水特殊规则）
- B：空报
- C：漏报  
- D：正确预报无降水
```

### 2. 量级TS评分
```
量级TS = A / (A + B + C)
其中：
- A：正确预报该量级（包括微量降水对小雨的贡献）
- B：预报为其他量级
- C：漏报（预报无雨）
```

## 🎯 预期效果

修复后的评分系统将：

1. **正确处理微量降水**：
   - 晴雨评分：微量降水+无雨/小雨 = 正确
   - 小雨评分：微量降水+无雨/小雨/微量降水 = 正确

2. **完整保存TS评分**：
   - 考生TS评分：完整统计和保存
   - CMA-MESO TS评分：完整统计和保存
   - 支持详细的对比分析

3. **准确的站点详情**：
   - 正确显示微量降水的预报结果类型
   - 准确计算TS贡献值
   - 提供特殊规则说明

## 🔧 测试建议

### 1. 微量降水测试用例
```
测试数据：
- 实况：微量降水(0.001mm)
- 考生预报：无雨(0.0mm) → 期望：晴雨正确A，小雨正确A
- 考生预报：小雨(5.0mm) → 期望：晴雨正确A，小雨正确A  
- 考生预报：中雨(15.0mm) → 期望：晴雨空报B，小雨错误B
```

### 2. TS评分对比测试
```
验证点：
- 考生TS评分是否正确计算和保存
- CMA-MESO TS评分是否正确计算和保存
- 两者的TS评分是否可以正确对比
```

### 3. 站点详情测试
```
验证点：
- 微量降水站点的结果类型显示是否正确
- TS贡献值计算是否准确
- 特殊规则说明是否显示
```

## 📝 注意事项

1. **历史数据兼容性**：已评分的历史数据需要重新评分以应用新规则
2. **数据精度**：微量降水识别使用浮点数精度控制（0.0001）
3. **权重配置**：微量降水不单独设置权重，通过小雨量级体现
4. **日志记录**：增加了详细的调试日志，便于问题排查

现在微量降水的评分规则已经完全修复，符合用户的需求！🎉
