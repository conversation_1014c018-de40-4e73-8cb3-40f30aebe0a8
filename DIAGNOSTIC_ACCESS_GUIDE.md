# 诊断接口访问指南

## ✅ 问题已解决！

我已经为诊断接口配置了**匿名访问权限**，现在你可以直接访问这些接口，无需 token。

## 🚀 可直接访问的诊断接口

### 1. 获取认证信息和接口列表
```bash
GET http://your-server/exam/api/weather/diagnostic/auth-info
```

### 2. 全面字符编码测试 ⭐ **推荐首先执行**
```bash
GET http://your-server/exam/api/weather/diagnostic/encoding-test/{answerId}
```
**作用**：分析系统环境、数据库连接、字符编码等关键信息

### 3. JSON字段处理对比分析 ⭐ **核心诊断**
```bash
GET http://your-server/exam/api/weather/diagnostic/compare-json/{answerId}
```
**作用**：对比考生答案表和题目表的JSON字段处理差异

### 4. 诊断特定答案记录
```bash
GET http://your-server/exam/api/weather/diagnostic/answer/{answerId}
```
**作用**：详细分析特定答案记录的数据状态

### 5. 批量诊断最近记录
```bash
GET http://your-server/exam/api/weather/diagnostic/recent?limit=10
```
**作用**：批量检查最近的答案记录，找出数据模式

### 6. 获取诊断建议
```bash
GET http://your-server/exam/api/weather/diagnostic/suggestions
```
**作用**：获取详细的诊断建议和手动检查SQL

## 📋 建议的诊断顺序

### 第一步：环境对比分析
```bash
# 执行全面字符编码测试
GET /exam/api/weather/diagnostic/encoding-test/1951420188121542657
```

**重点关注**：
- 操作系统差异（Windows vs Linux）
- Java字符编码设置
- MySQL字符集配置
- 数据库连接属性

### 第二步：字段处理对比
```bash
# 对比JSON字段处理差异
GET /exam/api/weather/diagnostic/compare-json/1951420188121542657
```

**重点关注**：
- 考生答案字段和题目字段的类型是否相同
- JSON解析是否都成功
- 字节长度和字符长度的差异

### 第三步：数据库原始检查
在数据库中直接执行：
```sql
-- 检查原始数据的十六进制表示
SELECT 
    id,
    HEX(precipitation_answer) as hex_precipitation,
    HEX(weather_answer) as hex_weather,
    LENGTH(precipitation_answer) as len_p,
    CHAR_LENGTH(precipitation_answer) as char_len_p
FROM el_weather_history_exam_answer 
WHERE id = '1951420188121542657';

-- 对比题目表的数据
SELECT 
    id,
    HEX(scenario_data) as hex_scenario,
    LENGTH(scenario_data) as len_s,
    CHAR_LENGTH(scenario_data) as char_len_s
FROM el_qu 
WHERE id = (SELECT question_id FROM el_weather_history_exam_answer WHERE id = '1951420188121542657');
```

## 🔍 预期的诊断结果

### 如果是字符编码问题，你会看到：
```log
=== 系统环境测试 ===
操作系统: Linux
默认字符编码: UTF-8
=== MySQL字符集设置 ===
character_set_connection: latin1  # ❌ 问题所在
character_set_results: latin1     # ❌ 问题所在
=== 对比不同表字段处理 ===
⚠️ 发现类型差异！
考生答案字段类型: String
题目字段类型: LinkedHashMap
```

### 如果是数据存储问题，你会看到：
```log
=== 字符编码处理测试 ===
原始数据长度: 179
UTF-8字节长度: 179
GBK字节长度: 200  # ❌ 长度不一致，说明编码有问题
❌ JSON解析失败: Unexpected character
```

## 🛠️ 根据诊断结果的修复方案

### 方案1：修复数据库连接字符编码
如果发现 `character_set_connection` 不是 `utf8mb4`：
```yaml
# application-server.yml
spring:
  datasource:
    url: *****************************************************************************************************************
```

### 方案2：修复表字符集
如果发现表字符集有问题：
```sql
ALTER TABLE el_weather_history_exam_answer CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 方案3：增强TypeHandler
如果需要处理多种编码：
```java
// 在 CustomJacksonTypeHandler 中添加编码尝试逻辑
```

## 📞 技术支持

### 配置修改说明
我已经修改了以下配置文件：

1. **ShiroConfig.java**：添加了诊断接口的匿名访问权限
2. **JwtFilter.java**：更新了匿名访问路径检查逻辑

### 安全提醒
- 诊断接口配置为匿名访问是**临时措施**
- 问题解决后，建议移除匿名访问配置
- 生产环境中请谨慎使用诊断接口

### 回滚方案
如果需要回滚匿名访问配置：
```java
// 在 ShiroConfig.java 中删除这一行：
// map.put("/exam/api/weather/diagnostic/**", "anon");

// 在 JwtFilter.java 中删除相关的匿名路径检查逻辑
```

## 🎯 下一步

1. **部署更新的代码**到生产环境
2. **执行诊断接口**，按照建议的顺序进行
3. **收集诊断结果**，分析问题根源
4. **应用对应的修复方案**
5. **验证修复效果**

现在你可以直接访问诊断接口了！🚀
