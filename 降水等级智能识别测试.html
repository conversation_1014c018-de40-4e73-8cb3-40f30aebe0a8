<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>降水等级智能识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case.pass {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .test-case.fail {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .result {
            font-weight: bold;
        }
        .expected {
            color: #28a745;
        }
        .actual {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>降水等级智能识别测试</h1>
    <p>测试新的智能识别逻辑：根据数值范围自动分类降水等级</p>
    
    <div id="test-results"></div>
    
    <script>
        // 复制前端的智能识别逻辑
        function mapLevelToString(level) {
            const numLevel = parseFloat(level)
            
            // 根据降水量范围智能识别等级
            if (numLevel >= 0.1 && numLevel < 10.0) {
                return 'level0' // 小雨：0.1-9.9mm
            } else if (numLevel >= 10.0 && numLevel < 25.0) {
                return 'level10' // 中雨：10-24.9mm
            } else if (numLevel >= 25.0 && numLevel < 50.0) {
                return 'level25' // 大雨：25-49.9mm
            } else if (numLevel >= 50.0 && numLevel < 100.0) {
                return 'level50' // 暴雨：50-99.9mm
            } else if (numLevel >= 100.0 && numLevel < 250.0) {
                return 'level100' // 大暴雨：100-249.9mm
            } else if (numLevel >= 250.0) {
                return 'level250' // 特大暴雨：≥250mm
            } else {
                // 小于0.1的值或无效值，默认返回原始level标识
                return `level${level}`
            }
        }

        // 测试用例
        const testCases = [
            // 小雨测试
            { input: 0.1, expected: 'level0', description: '边界值：0.1mm' },
            { input: 5.0, expected: 'level0', description: '中间值：5.0mm' },
            { input: 9.9, expected: 'level0', description: '边界值：9.9mm' },
            
            // 中雨测试
            { input: 10.0, expected: 'level10', description: '边界值：10.0mm' },
            { input: 15.5, expected: 'level10', description: '中间值：15.5mm' },
            { input: 24.9, expected: 'level10', description: '边界值：24.9mm' },
            
            // 大雨测试
            { input: 25.0, expected: 'level25', description: '边界值：25.0mm' },
            { input: 35.2, expected: 'level25', description: '中间值：35.2mm' },
            { input: 49.9, expected: 'level25', description: '边界值：49.9mm' },
            
            // 暴雨测试
            { input: 50.0, expected: 'level50', description: '边界值：50.0mm' },
            { input: 75.8, expected: 'level50', description: '中间值：75.8mm' },
            { input: 99.9, expected: 'level50', description: '边界值：99.9mm' },
            
            // 大暴雨测试
            { input: 100.0, expected: 'level100', description: '边界值：100.0mm' },
            { input: 150.5, expected: 'level100', description: '中间值：150.5mm' },
            { input: 249.9, expected: 'level100', description: '边界值：249.9mm' },
            
            // 特大暴雨测试
            { input: 250.0, expected: 'level250', description: '边界值：250.0mm' },
            { input: 300.5, expected: 'level250', description: '中间值：300.5mm' },
            { input: 500.0, expected: 'level250', description: '极大值：500.0mm' },
            
            // 边界情况测试
            { input: 0.0, expected: 'level0.0', description: '无降水：0.0mm' },
            { input: 0.05, expected: 'level0.05', description: '微量：0.05mm' },
            { input: '9.99', expected: 'level0', description: '字符串输入：9.99mm' },
            { input: 'abc', expected: 'levelNaN', description: '无效输入：abc' }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const actual = mapLevelToString(testCase.input);
                const passed = actual === testCase.expected;
                
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
                testDiv.innerHTML = `
                    <div><strong>测试 ${index + 1}:</strong> ${testCase.description}</div>
                    <div><strong>输入:</strong> ${testCase.input}</div>
                    <div><strong>期望:</strong> <span class="expected">${testCase.expected}</span></div>
                    <div><strong>实际:</strong> <span class="actual">${actual}</span></div>
                    <div class="result">${passed ? '✅ 通过' : '❌ 失败'}</div>
                `;
                resultsDiv.appendChild(testDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.style.cssText = 'margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;';
            summaryDiv.innerHTML = `
                <h3>测试总结</h3>
                <p><strong>通过:</strong> ${passCount}/${totalCount} (${(passCount/totalCount*100).toFixed(1)}%)</p>
                <p><strong>状态:</strong> ${passCount === totalCount ? '✅ 全部通过' : '❌ 存在失败'}</p>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
