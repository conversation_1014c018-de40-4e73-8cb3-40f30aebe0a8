# 降水落区评分详细信息功能说明

## 概述

本功能为降水落区评分系统增加了详细的评分信息记录和展示功能，可以记录每个站点的详细评分过程，包括实况、预报、评分结果等信息，便于在前端展示详细的评分分析。

## 功能特性

### 1. 站点级详细信息
- 记录每个站点的基本信息（站点ID、经纬度）
- 记录实况降水量和等级
- 记录考生预报降水量和等级
- 记录CMA-MESO预报降水量和等级
- 记录晴雨评分详情（正确A/空报B/漏报C/正确D）
- 记录量级评分详情（正确A/错误B/漏报C）
- 记录特殊规则说明（如微量降水特殊规则）

### 2. 量级TS评分详情
- 记录每个量级的TS评分统计（A、B、C数量）
- 记录TS评分计算公式和结果
- 记录基础分、技巧评分、权重和对最终评分的贡献
- 记录参与评分的站点数量
- 记录特殊规则说明

### 3. 评分摘要信息
- 生成详细的评分摘要报告
- 提供各量级TS评分对比表
- 提供站点统计摘要
- 支持导出CSV格式的详细数据

## 数据结构

### StationScoringDetail（站点评分详情）
```java
{
    "stationId": 54511,
    "longitude": 116.28,
    "latitude": 39.93,
    "actualPrecipitation": 0.001,
    "actualLevel": "微量降水",
    "studentForecastPrecipitation": 1.0,
    "studentForecastLevel": "小雨",
    "cmaMesoForecastPrecipitation": 15.0,
    "cmaMesoForecastLevel": "中雨",
    "rainNoRainDetail": {
        "actualHasRain": true,
        "studentForecastHasRain": true,
        "cmaMesoForecastHasRain": true,
        "studentResultType": "正确A",
        "cmaMesoResultType": "正确A",
        "studentContribution": "正确+1",
        "cmaMesoContribution": "正确+1"
    },
    "levelDetail": {
        "level": "微量降水",
        "participateInScoring": true,
        "studentResultType": "正确A",
        "cmaMesoResultType": "错误B",
        "studentContribution": "正确+1",
        "cmaMesoContribution": "错误+1",
        "specialRuleNote": "微量降水特殊规则：预报为微量降水、小雨或无雨都算正确"
    }
}
```

### LevelTSScoringDetail（量级TS评分详情）
```java
{
    "level": "微量降水",
    "totalStations": 10,
    "studentTSStats": {
        "correctForecast": 9,
        "wrongForecast": 1,
        "missedForecast": 0,
        "tsScore": 0.900,
        "formulaDescription": "TS = 9 / (9 + 1) = 0.900"
    },
    "cmaMesoTSStats": {
        "correctForecast": 5,
        "wrongForecast": 5,
        "missedForecast": 0,
        "tsScore": 0.500,
        "formulaDescription": "TS = 5 / (5 + 5) = 0.500"
    },
    "studentBaseScore": 0.3,
    "studentSkillScore": 0.930,
    "weight": 0.05,
    "contributionToFinalScore": 1.86,
    "specialRuleNote": "微量降水特殊规则：预报为微量降水、小雨或无雨都算正确"
}
```

## API接口

### 1. 计算详细评分信息
**接口**: `POST /weather/scoring/precipitation/detail/calculate`

**参数**:
- `actualFilePath`: 实况降水文件路径
- `cmaMesoFilePath`: CMA-MESO文件路径
- `studentAnswer`: 考生答案JSON
- `regionCode`: 区域编码（可选）

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "finalScore": 28.5,
        "totalStations": 100,
        "success": true,
        "message": "降水落区评分计算完成",
        "studentTSScores": {
            "晴雨": 0.850,
            "微量降水": 0.900,
            "小雨": 0.750,
            "中雨": 0.600,
            "大雨": 0.450,
            "暴雨": 0.300,
            "大暴雨": 0.200
        },
        "cmaMesoTSScores": {
            "晴雨": 0.800,
            "微量降水": 0.500,
            "小雨": 0.700,
            "中雨": 0.650,
            "大雨": 0.500,
            "暴雨": 0.250,
            "大暴雨": 0.150
        },
        "baseScores": {
            "晴雨": 0.3,
            "微量降水": 0.3,
            "小雨": 0.3,
            "中雨": 0.0,
            "大雨": 0.0,
            "暴雨": 0.3,
            "大暴雨": 0.3
        },
        "skillScores": {
            "晴雨": 0.895,
            "微量降水": 0.930,
            "小雨": 0.825,
            "中雨": 0.420,
            "大雨": 0.315,
            "暴雨": 0.510,
            "大暴雨": 0.440
        },
        "weights": {
            "晴雨": 0.1,
            "微量降水": 0.05,
            "小雨": 0.2,
            "中雨": 0.2,
            "大雨": 0.2,
            "暴雨": 0.2,
            "大暴雨": 0.05
        },
        "stationDetails": [...],
        "levelTSDetails": [...],
        "scoringSummary": "=== 降水落区评分详细摘要 ===\n..."
    }
}
```

### 2. 导出站点详细信息
**接口**: `POST /weather/scoring/precipitation/detail/export/stations`

**功能**: 导出所有站点的详细评分信息为CSV格式

**CSV格式**:
```csv
站点ID,经度,纬度,实况降水量,实况等级,考生预报降水量,考生预报等级,CMA-MESO预报降水量,CMA-MESO预报等级,晴雨-考生结果,晴雨-CMA结果,量级-考生结果,量级-CMA结果,特殊规则说明
54511,116.2800,39.9300,0.001,微量降水,1.0,小雨,15.0,中雨,正确A,正确A,正确A,错误B,微量降水特殊规则：预报为微量降水、小雨或无雨都算正确
54527,117.1200,40.4800,5.0,小雨,5.0,小雨,3.0,小雨,正确A,正确A,正确A,正确A,
...
```

## 前端展示建议

### 1. 评分概览页面
- 显示最终评分和总站点数
- 显示各量级TS评分对比表
- 显示评分摘要信息
- 提供详细信息查看入口

### 2. 站点详情页面
- 提供站点列表，支持筛选和排序
- 显示每个站点的详细评分信息
- 支持地图展示站点分布
- 支持按评分结果类型筛选站点

### 3. 量级分析页面
- 显示各量级的TS评分详情
- 显示TS评分计算过程
- 显示各量级对最终评分的贡献
- 支持量级间的对比分析

### 4. 数据导出功能
- 支持导出站点详细信息为CSV
- 支持导出量级TS评分详情
- 支持导出评分摘要报告

## 使用示例

### 1. 基本使用
```javascript
// 调用详细评分接口
const response = await fetch('/weather/scoring/precipitation/detail/calculate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        actualFilePath: '/path/to/actual.txt',
        cmaMesoFilePath: '/path/to/cma-meso.txt',
        studentAnswer: { /* 考生答案 */ },
        regionCode: '1'
    })
});

const result = await response.json();
if (result.code === 200) {
    const data = result.data;
    console.log('最终评分:', data.finalScore);
    console.log('站点详情:', data.stationDetails);
    console.log('量级详情:', data.levelTSDetails);
}
```

### 2. 站点详情展示
```javascript
// 展示站点详情
data.stationDetails.forEach(station => {
    console.log(`站点${station.stationId}:`);
    console.log(`  位置: ${station.longitude}, ${station.latitude}`);
    console.log(`  实况: ${station.actualDescription}`);
    console.log(`  考生预报: ${station.studentForecastDescription}`);
    console.log(`  CMA-MESO预报: ${station.cmaMesoForecastDescription}`);
    
    if (station.levelDetail) {
        console.log(`  量级评分: ${station.levelDetail.studentResultType}`);
        if (station.levelDetail.specialRuleNote) {
            console.log(`  特殊规则: ${station.levelDetail.specialRuleNote}`);
        }
    }
});
```

### 3. 量级分析展示
```javascript
// 展示量级TS评分详情
data.levelTSDetails.forEach(level => {
    console.log(`${level.level}量级:`);
    console.log(`  参与站点: ${level.totalStations}个`);
    console.log(`  学生TS: ${level.studentTSStats.tsScore.toFixed(3)}`);
    console.log(`  计算公式: ${level.studentTSStats.formulaDescription}`);
    console.log(`  技巧评分: ${level.studentSkillScore.toFixed(3)}`);
    console.log(`  权重: ${level.weight}`);
    console.log(`  贡献: ${level.contributionToFinalScore.toFixed(2)}分`);
});
```

## 注意事项

1. **性能考虑**: 详细信息收集会增加计算时间，建议在需要时才启用
2. **内存使用**: 大量站点时详细信息会占用较多内存，注意内存管理
3. **数据一致性**: 确保详细信息与总体评分结果一致
4. **特殊规则**: 微量降水等特殊规则在详细信息中有明确标注

## 扩展功能

1. **缓存机制**: 可以将详细评分结果缓存，避免重复计算
2. **数据库存储**: 可以将详细信息存储到数据库，支持历史查询
3. **可视化展示**: 可以基于详细信息生成图表和地图可视化
4. **对比分析**: 可以支持多次评分结果的对比分析
