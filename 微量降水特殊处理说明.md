# 微量降水特殊处理实现说明

## 概述

本次修改实现了对微量降水（0.001mm）的特殊处理，包括数据格式化、等级分类和评分逻辑的特殊规则。

## 修改内容

### 1. MdfsBinaryParser.java - formatValueForType3方法

**文件位置**: `src/main/java/com/yf/exam/modules/weather/micaps/MdfsBinaryParser.java`

**修改内容**:
- 在 `formatValueForType3` 方法中添加了对 0.001 值的特殊处理
- 当降水量为 0.001 时，返回 "T"（Trace，表示微量降水）
- 保持其他数值的格式化逻辑不变

```java
// 特殊处理微量降水（0.001表示微量降水）
if (Math.abs(value - 0.001) < 0.0001) {
    return "T"; // T表示微量降水（Trace）
}
```

### 2. PrecipitationAreaScoringService.java - 降水等级分类

**文件位置**: `src/main/java/com/yf/exam/modules/weather/scoring/service/PrecipitationAreaScoringService.java`

**修改内容**:
- 在 `classifyPrecipitationLevel` 方法中添加微量降水的识别
- 当降水量为 0.001 时，返回 "微量降水" 等级

```java
// 特殊处理微量降水（0.001表示微量降水）
if (Math.abs(precipitation - 0.001) < 0.0001) {
    return "微量降水";
}
```

### 3. 微量降水特殊评分逻辑

**新增方法**: `calculateTracePrecipitationTS`

**特殊评分规则**:
- 实况为微量降水时，考生预报为以下任一情况都算正确：
  - 微量降水
  - 小雨
  - 无雨

**实现逻辑**:
```java
// 微量降水的特殊评分规则：预报为小雨或无雨都算正确
if ("微量降水".equals(forecastLevel) || "小雨".equals(forecastLevel) || "无雨".equals(forecastLevel)) {
    A++; // 正确预报
} else {
    B++; // 预报为其他量级（中雨及以上）
}
```

### 4. 评分权重配置更新

**修改内容**:
- 在 `calculateWeightedFinalScore` 方法中添加微量降水的权重
- 微量降水权重：0.05
- 调整大暴雨权重为 0.05（保持总权重为 1.0）

### 5. TS评分计算更新

**修改内容**:
- 在 `calculateAllLevelTS` 方法中添加微量降水的TS评分计算
- 更新晴雨TS评分逻辑以正确处理微量降水

### 6. StationPrecipitationData.java 数据对象更新

**文件位置**: `src/main/java/com/yf/exam/modules/weather/scoring/dto/StationPrecipitationData.java`

**修改内容**:
- 更新 `hasActualRain()` 和 `hasForecastRain()` 方法以包含微量降水
- 新增 `isTraceRain()` 方法用于判断是否为微量降水

## 技术细节

### 微量降水识别精度

使用浮点数比较时采用精度控制：
```java
Math.abs(precipitation - 0.001) < 0.0001
```

### 日志记录

在微量降水评分方法中添加了详细的日志记录，便于调试和监控：
- 记录微量降水站点的预报结果
- 统计微量降水的TS评分详情

## 影响范围

1. **数据解析**: MICAPS第三类数据解析时，0.001值会被格式化为"T"
2. **降水分类**: 新增"微量降水"等级
3. **评分计算**: 微量降水采用特殊的评分规则
4. **权重分配**: 调整了各降水等级的权重分配

## 测试建议

1. 使用包含0.001降水量的测试数据验证格式化功能
2. 测试微量降水的评分逻辑是否按预期工作
3. 验证权重计算的正确性
4. 检查日志输出是否正常

## 注意事项

1. 微量降水的特殊评分规则只适用于实况为微量降水的情况
2. 浮点数比较使用了精度控制，避免精度问题
3. 权重调整保持了总权重为1.0的约束
4. 所有修改都保持了向后兼容性
