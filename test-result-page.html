<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考生TS评分详细记录测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .student-details-section {
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(103, 194, 58, 0.2);
            box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
        }
        
        .student-details-section h5 {
            margin: 0 0 20px 0;
            color: #67c23a;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .student-details-section h5::before {
            content: '👤';
            font-size: 20px;
        }
        
        .student-level-tabs {
            margin-top: 15px;
        }
        
        .tab-container {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .tab-header {
            display: flex;
            background: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .tab-item {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #e4e7ed;
            transition: all 0.3s;
        }
        
        .tab-item:last-child {
            border-right: none;
        }
        
        .tab-item.active {
            background: #67c23a;
            color: white;
        }
        
        .tab-item:hover:not(.active) {
            background: #e8f5e8;
        }
        
        .tab-content {
            padding: 20px;
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .student-summary {
            background: rgba(103, 194, 58, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #67c23a;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .summary-item:last-child {
            margin-bottom: 0;
        }
        
        .summary-label {
            font-weight: 600;
            color: #606266;
        }
        
        .summary-value {
            font-weight: 700;
            color: #67c23a;
            font-size: 16px;
        }
        
        .summary-formula {
            font-family: 'Courier New', monospace;
            background: rgba(103, 194, 58, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .student-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }
        
        .stat-item {
            background: rgba(103, 194, 58, 0.05);
            border: 1px solid rgba(103, 194, 58, 0.2);
            border-radius: 6px;
            padding: 12px;
            text-align: center;
        }
        
        .stat-label {
            display: block;
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
        }
        
        .stat-value {
            display: block;
            font-size: 18px;
            font-weight: 700;
            color: #67c23a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>考生TS评分详细记录展示</h1>
        
        <!-- 考生TS评分详细记录 -->
        <div class="student-details-section">
            <h5>我的TS评分详细记录</h5>
            <div class="student-level-details">
                <div class="student-level-tabs">
                    <div class="tab-container">
                        <div class="tab-header">
                            <div class="tab-item active" onclick="showTab('rain')">晴雨</div>
                            <div class="tab-item" onclick="showTab('light')">小雨</div>
                            <div class="tab-item" onclick="showTab('moderate')">中雨</div>
                            <div class="tab-item" onclick="showTab('heavy')">大雨</div>
                            <div class="tab-item" onclick="showTab('storm')">暴雨</div>
                            <div class="tab-item" onclick="showTab('heavy-storm')">大暴雨</div>
                        </div>
                        
                        <!-- 晴雨TS评分 -->
                        <div id="rain" class="tab-content active">
                            <div class="student-summary">
                                <div class="summary-item">
                                    <span class="summary-label">TS评分：</span>
                                    <span class="summary-value">0.566</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">计算公式：</span>
                                    <span class="summary-formula">TS = (20 + 40) / (20 + 40 + 15 + 31) = 0.566</span>
                                </div>
                            </div>
                            <div class="student-stats">
                                <div class="stat-item">
                                    <span class="stat-label">正确预报有雨：</span>
                                    <span class="stat-value">20个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">正确预报无雨：</span>
                                    <span class="stat-value">40个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">空报：</span>
                                    <span class="stat-value">15个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">漏报：</span>
                                    <span class="stat-value">31个</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 小雨TS评分 -->
                        <div id="light" class="tab-content">
                            <div class="student-summary">
                                <div class="summary-item">
                                    <span class="summary-label">TS评分：</span>
                                    <span class="summary-value">0.132</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">计算公式：</span>
                                    <span class="summary-formula">TS = 5 / (5 + 8 + 25) = 0.132</span>
                                </div>
                            </div>
                            <div class="student-stats">
                                <div class="stat-item">
                                    <span class="stat-label">正确预报：</span>
                                    <span class="stat-value">5个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">错误预报：</span>
                                    <span class="stat-value">8个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">漏报：</span>
                                    <span class="stat-value">25个</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中雨TS评分 -->
                        <div id="moderate" class="tab-content">
                            <div class="student-summary">
                                <div class="summary-item">
                                    <span class="summary-label">TS评分：</span>
                                    <span class="summary-value">0.000</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">计算公式：</span>
                                    <span class="summary-formula">TS = 0 / (0 + 3 + 8) = 0.000</span>
                                </div>
                            </div>
                            <div class="student-stats">
                                <div class="stat-item">
                                    <span class="stat-label">正确预报：</span>
                                    <span class="stat-value">0个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">错误预报：</span>
                                    <span class="stat-value">3个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">漏报：</span>
                                    <span class="stat-value">8个</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 大雨TS评分 -->
                        <div id="heavy" class="tab-content">
                            <div class="student-summary">
                                <div class="summary-item">
                                    <span class="summary-label">TS评分：</span>
                                    <span class="summary-value">0.000</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">计算公式：</span>
                                    <span class="summary-formula">TS = 0 / (0 + 2 + 5) = 0.000</span>
                                </div>
                            </div>
                            <div class="student-stats">
                                <div class="stat-item">
                                    <span class="stat-label">正确预报：</span>
                                    <span class="stat-value">0个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">错误预报：</span>
                                    <span class="stat-value">2个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">漏报：</span>
                                    <span class="stat-value">5个</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 暴雨TS评分 -->
                        <div id="storm" class="tab-content">
                            <div class="student-summary">
                                <div class="summary-item">
                                    <span class="summary-label">TS评分：</span>
                                    <span class="summary-value">0.115</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">计算公式：</span>
                                    <span class="summary-formula">TS = 3 / (3 + 5 + 18) = 0.115</span>
                                </div>
                            </div>
                            <div class="student-stats">
                                <div class="stat-item">
                                    <span class="stat-label">正确预报：</span>
                                    <span class="stat-value">3个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">错误预报：</span>
                                    <span class="stat-value">5个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">漏报：</span>
                                    <span class="stat-value">18个</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 大暴雨TS评分 -->
                        <div id="heavy-storm" class="tab-content">
                            <div class="student-summary">
                                <div class="summary-item">
                                    <span class="summary-label">TS评分：</span>
                                    <span class="summary-value">0.000</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">计算公式：</span>
                                    <span class="summary-formula">TS = 0 / (0 + 1 + 0) = 0.000</span>
                                </div>
                            </div>
                            <div class="student-stats">
                                <div class="stat-item">
                                    <span class="stat-label">正确预报：</span>
                                    <span class="stat-value">0个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">错误预报：</span>
                                    <span class="stat-value">1个</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">漏报：</span>
                                    <span class="stat-value">0个</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabId) {
            // 隐藏所有tab内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有tab的active状态
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的tab内容
            document.getElementById(tabId).classList.add('active');
            
            // 设置选中的tab为active状态
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
