package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.weather.scoring.dto.StationPrecipitationData;

import java.util.ArrayList;
import java.util.List;

/**
 * 降水量级TS评分修复测试
 *
 * 测试修复后的中雨TS评分逻辑，确保只有实况为中雨的站点才参与中雨TS评分
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class PrecipitationLevelTSFixTest {

    public static void main(String[] args) {
        System.out.println("开始测试降水量级TS评分修复...");

        PrecipitationLevelTSFixTest test = new PrecipitationLevelTSFixTest();

        try {
            test.testModerateLevelTSScoring();
            test.testLightRainTSWithTraceRain();
            test.testDataConsistency();
            System.out.println("所有测试通过！");
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试中雨TS评分修复（直接计算，不依赖Spring）
     *
     * 场景：实况中雨6个站点，考生预报3个正确，3个漏报
     * 期望：中雨TS评分只统计这6个站点，不应该包含其他量级的站点
     */
    public void testModerateLevelTSScoring() throws Exception {
        System.out.println("测试中雨TS评分修复...");

        // 准备测试数据
        List<StationPrecipitationData> actualData = createActualData();
        List<StationPrecipitationData> studentData = createStudentData();

        // 验证实况数据
        long moderateRainCount = actualData.stream()
            .mapToLong(data -> "中雨".equals(data.getActualLevel()) ? 1 : 0)
            .sum();

        if (moderateRainCount != 6) {
            throw new AssertionError("实况中雨站点数应该为6个，实际为：" + moderateRainCount);
        }

        // 直接计算中雨TS评分，不依赖Spring框架
        double moderateTS = calculateLevelTSDirectly(studentData, actualData, "中雨");

        // 验证TS评分结果
        // 期望：3个正确预报，0个错误预报，3个漏报
        // TS = 3 / (3 + 0 + 3) = 0.5
        if (Math.abs(moderateTS - 0.5) > 0.001) {
            throw new AssertionError("中雨TS评分应该为0.5，实际为：" + moderateTS);
        }

        System.out.println("中雨TS评分测试通过：" + moderateTS);
    }

    /**
     * 测试小雨TS评分（包含微量降水特殊规则）
     */
    public void testLightRainTSWithTraceRain() throws Exception {
        System.out.println("测试小雨TS评分（包含微量降水特殊规则）...");

        List<StationPrecipitationData> actualData = createActualData();
        List<StationPrecipitationData> studentData = createStudentData();

        // 验证小雨和微量降水站点数
        long lightRainCount = actualData.stream()
            .mapToLong(data -> "小雨".equals(data.getActualLevel()) ? 1 : 0)
            .sum();
        long traceRainCount = actualData.stream()
            .mapToLong(data -> "微量降水".equals(data.getActualLevel()) ? 1 : 0)
            .sum();

        if (lightRainCount != 4) {
            throw new AssertionError("实况小雨站点数应该为4个，实际为：" + lightRainCount);
        }
        if (traceRainCount != 2) {
            throw new AssertionError("实况微量降水站点数应该为2个，实际为：" + traceRainCount);
        }

        // 直接计算小雨TS评分
        double lightRainTS = calculateLevelTSDirectly(studentData, actualData, "小雨");

        // 小雨TS评分应该包含小雨和微量降水的站点（共6个）
        // 根据测试数据设计，应该有合理的TS评分
        if (lightRainTS < 0.0 || lightRainTS > 1.0) {
            throw new AssertionError("小雨TS评分应该在0-1之间，实际为：" + lightRainTS);
        }

        System.out.println("小雨TS评分测试通过：" + lightRainTS);
    }

    /**
     * 测试数据一致性（不依赖Spring框架的简单测试）
     */
    public void testDataConsistency() throws Exception {
        System.out.println("测试数据一致性...");

        List<StationPrecipitationData> actualData = createActualData();
        List<StationPrecipitationData> studentData = createStudentData();

        // 验证数据一致性
        if (actualData.size() != studentData.size()) {
            throw new AssertionError("实况数据和考生数据站点数不一致");
        }

        // 统计各量级站点数
        long[] actualCounts = new long[7]; // 无雨、微量降水、小雨、中雨、大雨、暴雨、大暴雨
        String[] levels = {"无雨", "微量降水", "小雨", "中雨", "大雨", "暴雨", "大暴雨"};

        for (StationPrecipitationData data : actualData) {
            String level = data.getActualLevel();
            for (int i = 0; i < levels.length; i++) {
                if (levels[i].equals(level)) {
                    actualCounts[i]++;
                    break;
                }
            }
        }

        // 验证预期的站点数
        long[] expectedCounts = {8, 2, 4, 6, 3, 2, 1}; // 预期的各量级站点数
        for (int i = 0; i < levels.length; i++) {
            if (actualCounts[i] != expectedCounts[i]) {
                throw new AssertionError(String.format("%s站点数不符合预期，预期：%d，实际：%d",
                    levels[i], expectedCounts[i], actualCounts[i]));
            }
        }

        // 验证中雨站点的考生预报情况
        int moderateCorrect = 0, moderateMissed = 0;
        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData student = studentData.get(i);

            if ("中雨".equals(actual.getActualLevel())) {
                if ("中雨".equals(student.getForecastLevel())) {
                    moderateCorrect++;
                } else if ("无雨".equals(student.getForecastLevel())) {
                    moderateMissed++;
                }
            }
        }

        if (moderateCorrect != 3) {
            throw new AssertionError("中雨正确预报数应该为3，实际为：" + moderateCorrect);
        }
        if (moderateMissed != 3) {
            throw new AssertionError("中雨漏报数应该为3，实际为：" + moderateMissed);
        }

        System.out.println("数据一致性测试通过");
        System.out.println("各量级站点数：");
        for (int i = 0; i < levels.length; i++) {
            System.out.println("  " + levels[i] + ": " + actualCounts[i] + "个");
        }
        System.out.println("中雨预报情况：正确" + moderateCorrect + "个，漏报" + moderateMissed + "个");
    }

    /**
     * 直接计算量级TS评分（复制修复后的逻辑）
     */
    private double calculateLevelTSDirectly(List<StationPrecipitationData> forecastData,
                                           List<StationPrecipitationData> actualData,
                                           String level) {
        int A = 0, B = 0, C = 0;

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);

            String actualLevel = actual.getActualLevel();
            String forecastLevel = forecast.getForecastLevel();

            // 处理实况为该量级的站点
            if (level.equals(actualLevel)) {
                if (level.equals(forecastLevel)) {
                    A++; // 正确预报该量级
                } else if (!"无雨".equals(forecastLevel)) {
                    B++; // 预报为其他量级
                } else {
                    C++; // 漏报（预报无雨）
                }
            }
            // 处理实况为微量降水的特殊情况（仅对小雨量级）
            else if ("微量降水".equals(actualLevel) && "小雨".equals(level)) {
                // 微量降水特殊规则：预报为小雨、无雨或微量降水都算正确
                if ("小雨".equals(forecastLevel) || "无雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
                    A++; // 算作小雨的正确预报（包括预报微量降水的情况）
                } else {
                    B++; // 预报为其他量级（中雨及以上）
                }
            }
            // 对于其他量级（中雨、大雨、暴雨、大暴雨），不处理微量降水的特殊情况
            // 这样确保只有实况为该量级的站点才参与该量级的TS评分
        }

        int total = A + B + C;
        double tsScore = total > 0 ? (double)A / total : 0.0;

        System.out.println(String.format("%s TS评分详情：A=%d, B=%d, C=%d, Total=%d, TS=%.3f",
            level, A, B, C, total, tsScore));

        return tsScore;
    }

    /**
     * 创建实况数据
     * 包含：无雨8个，微量降水2个，小雨4个，中雨6个，大雨3个，暴雨2个，大暴雨1个
     */
    private List<StationPrecipitationData> createActualData() {
        List<StationPrecipitationData> actualData = new ArrayList<>();
        
        // 无雨站点 (8个)
        for (int i = 1; i <= 8; i++) {
            StationPrecipitationData data = new StationPrecipitationData();
            data.setStationId(50000 + i);
            data.setLongitude(110.0 + i * 0.1);
            data.setLatitude(30.0 + i * 0.1);
            data.setActualPrecipitation(0.0);
            data.setActualLevel("无雨");
            actualData.add(data);
        }
        
        // 微量降水站点 (2个)
        for (int i = 1; i <= 2; i++) {
            StationPrecipitationData data = new StationPrecipitationData();
            data.setStationId(51000 + i);
            data.setLongitude(111.0 + i * 0.1);
            data.setLatitude(31.0 + i * 0.1);
            data.setActualPrecipitation(0.001); // 微量降水
            data.setActualLevel("微量降水");
            actualData.add(data);
        }
        
        // 小雨站点 (4个)
        for (int i = 1; i <= 4; i++) {
            StationPrecipitationData data = new StationPrecipitationData();
            data.setStationId(52000 + i);
            data.setLongitude(112.0 + i * 0.1);
            data.setLatitude(32.0 + i * 0.1);
            data.setActualPrecipitation(5.0 + i); // 5-8mm
            data.setActualLevel("小雨");
            actualData.add(data);
        }
        
        // 中雨站点 (6个) - 这是测试重点
        for (int i = 1; i <= 6; i++) {
            StationPrecipitationData data = new StationPrecipitationData();
            data.setStationId(53000 + i);
            data.setLongitude(113.0 + i * 0.1);
            data.setLatitude(33.0 + i * 0.1);
            data.setActualPrecipitation(15.0 + i); // 16-21mm
            data.setActualLevel("中雨");
            actualData.add(data);
        }
        
        // 大雨站点 (3个)
        for (int i = 1; i <= 3; i++) {
            StationPrecipitationData data = new StationPrecipitationData();
            data.setStationId(54000 + i);
            data.setLongitude(114.0 + i * 0.1);
            data.setLatitude(34.0 + i * 0.1);
            data.setActualPrecipitation(30.0 + i * 5); // 35-45mm
            data.setActualLevel("大雨");
            actualData.add(data);
        }
        
        // 暴雨站点 (2个)
        for (int i = 1; i <= 2; i++) {
            StationPrecipitationData data = new StationPrecipitationData();
            data.setStationId(55000 + i);
            data.setLongitude(115.0 + i * 0.1);
            data.setLatitude(35.0 + i * 0.1);
            data.setActualPrecipitation(60.0 + i * 10); // 70-80mm
            data.setActualLevel("暴雨");
            actualData.add(data);
        }
        
        // 大暴雨站点 (1个)
        StationPrecipitationData data = new StationPrecipitationData();
        data.setStationId(56001);
        data.setLongitude(116.0);
        data.setLatitude(36.0);
        data.setActualPrecipitation(120.0);
        data.setActualLevel("大暴雨");
        actualData.add(data);
        
        return actualData;
    }

    /**
     * 创建考生预报数据
     * 中雨：3个正确预报，3个漏报（预报为无雨）
     */
    private List<StationPrecipitationData> createStudentData() {
        List<StationPrecipitationData> actualData = createActualData();
        List<StationPrecipitationData> studentData = new ArrayList<>();
        
        for (StationPrecipitationData actual : actualData) {
            StationPrecipitationData student = new StationPrecipitationData();
            student.setStationId(actual.getStationId());
            student.setLongitude(actual.getLongitude());
            student.setLatitude(actual.getLatitude());
            student.setActualPrecipitation(actual.getActualPrecipitation());
            student.setActualLevel(actual.getActualLevel());
            
            // 设置考生预报
            if ("中雨".equals(actual.getActualLevel())) {
                // 中雨站点：前3个预报正确，后3个漏报
                if (actual.getStationId() <= 53003) {
                    student.setForecastLevel("中雨");
                    student.setForecastPrecipitation(17.5); // 中雨代表值
                } else {
                    student.setForecastLevel("无雨");
                    student.setForecastPrecipitation(0.0);
                }
            } else {
                // 其他站点保持实况（简化处理）
                student.setForecastLevel(actual.getActualLevel());
                student.setForecastPrecipitation(actual.getActualPrecipitation());
            }
            
            studentData.add(student);
        }
        
        return studentData;
    }
}
