package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.weather.scoring.dto.LevelTSScoringDetail;
import com.yf.exam.modules.weather.scoring.dto.StationPrecipitationData;

import java.util.ArrayList;
import java.util.List;

/**
 * 晴雨TS评分D项修复测试
 *
 * 测试修复后的晴雨TS评分逻辑，确保D项（正确预报无雨）被正确统计和显示
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class RainNoRainTSFixTest {

    public static void main(String[] args) {
        System.out.println("开始测试晴雨TS评分D项修复...");

        RainNoRainTSFixTest test = new RainNoRainTSFixTest();

        try {
            test.testTSStatisticsWithDItem();
            test.testRainNoRainTSCalculation();
            test.testTraceRainSpecialHandling();
            System.out.println("所有测试通过！");
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试TSStatistics类的D项统计功能
     */
    public void testTSStatisticsWithDItem() throws Exception {
        System.out.println("测试TSStatistics类的D项统计功能...");

        LevelTSScoringDetail.TSStatistics stats = new LevelTSScoringDetail.TSStatistics();

        // 模拟晴雨TS评分统计
        stats.addCorrect();      // A项：正确预报有雨 +1
        stats.addCorrect();      // A项：正确预报有雨 +1
        stats.addWrong();        // B项：空报 +1
        stats.addMissed();       // C项：漏报 +1
        stats.addCorrectNoRain(); // D项：正确预报无雨 +1
        stats.addCorrectNoRain(); // D项：正确预报无雨 +1

        // 计算TS评分
        stats.calculateTS();

        // 验证统计数据
        if (stats.getCorrectForecast() != 2) {
            throw new AssertionError("正确预报有雨数应该为2，实际为：" + stats.getCorrectForecast());
        }
        if (stats.getWrongForecast() != 1) {
            throw new AssertionError("空报数应该为1，实际为：" + stats.getWrongForecast());
        }
        if (stats.getMissedForecast() != 1) {
            throw new AssertionError("漏报数应该为1，实际为：" + stats.getMissedForecast());
        }
        if (stats.getCorrectNoRainForecast() != 2) {
            throw new AssertionError("正确预报无雨数应该为2，实际为：" + stats.getCorrectNoRainForecast());
        }

        // 验证TS评分计算：TS = (A + D) / (A + B + C + D) = (2 + 2) / (2 + 1 + 1 + 2) = 4/6 = 0.667
        double expectedTS = 4.0 / 6.0;
        if (Math.abs(stats.getTsScore() - expectedTS) > 0.001) {
            throw new AssertionError("TS评分应该为" + expectedTS + "，实际为：" + stats.getTsScore());
        }

        // 验证公式描述
        String expectedFormula = "TS = (2 + 2) / (2 + 1 + 1 + 2) = 0.667";
        if (!stats.getFormulaDescription().contains("(2 + 2)") || 
            !stats.getFormulaDescription().contains("0.667")) {
            throw new AssertionError("公式描述不正确：" + stats.getFormulaDescription());
        }

        // 验证摘要
        String summary = stats.getSummary();
        if (!summary.contains("正确有雨:2") || !summary.contains("正确无雨:2")) {
            throw new AssertionError("摘要不正确：" + summary);
        }

        System.out.println("TSStatistics D项统计测试通过");
        System.out.println("  统计结果：" + summary);
        System.out.println("  公式：" + stats.getFormulaDescription());
    }

    /**
     * 测试晴雨TS评分计算（直接计算，不依赖Spring）
     */
    public void testRainNoRainTSCalculation() throws Exception {
        System.out.println("测试晴雨TS评分计算...");

        // 准备测试数据
        List<StationPrecipitationData> actualData = createTestActualData();
        List<StationPrecipitationData> studentData = createTestStudentData();

        // 直接计算晴雨TS评分
        RainNoRainTSResult result = calculateRainNoRainTSDirectly(studentData, actualData);

        // 验证统计结果
        // 根据测试数据设计：A=2, B=1, C=1, D=2
        if (result.A != 2) {
            throw new AssertionError("正确预报有雨数应该为2，实际为：" + result.A);
        }
        if (result.B != 1) {
            throw new AssertionError("空报数应该为1，实际为：" + result.B);
        }
        if (result.C != 1) {
            throw new AssertionError("漏报数应该为1，实际为：" + result.C);
        }
        if (result.D != 2) {
            throw new AssertionError("正确预报无雨数应该为2，实际为：" + result.D);
        }

        // 验证TS评分：TS = (A + D) / (A + B + C + D) = (2 + 2) / (2 + 1 + 1 + 2) = 4/6 = 0.667
        double expectedTS = 4.0 / 6.0;
        if (Math.abs(result.tsScore - expectedTS) > 0.001) {
            throw new AssertionError("TS评分应该为" + expectedTS + "，实际为：" + result.tsScore);
        }

        System.out.println("晴雨TS评分计算测试通过");
        System.out.println("  A=" + result.A + ", B=" + result.B + ", C=" + result.C + ", D=" + result.D);
        System.out.println("  TS评分=" + result.tsScore);
    }

    /**
     * 测试微量降水特殊处理
     */
    public void testTraceRainSpecialHandling() throws Exception {
        System.out.println("测试微量降水特殊处理...");

        // 创建包含微量降水的测试数据
        List<StationPrecipitationData> actualData = new ArrayList<>();
        List<StationPrecipitationData> studentData = new ArrayList<>();

        // 微量降水站点1：预报无雨（应该算正确A）
        StationPrecipitationData actual1 = createStation(1, 0.001, "微量降水");
        StationPrecipitationData student1 = createStation(1, 0.0, "无雨");
        actualData.add(actual1);
        studentData.add(student1);

        // 微量降水站点2：预报小雨（应该算正确A）
        StationPrecipitationData actual2 = createStation(2, 0.001, "微量降水");
        StationPrecipitationData student2 = createStation(2, 5.0, "小雨");
        actualData.add(actual2);
        studentData.add(student2);

        // 微量降水站点3：预报中雨（应该算空报B）
        StationPrecipitationData actual3 = createStation(3, 0.001, "微量降水");
        StationPrecipitationData student3 = createStation(3, 15.0, "中雨");
        actualData.add(actual3);
        studentData.add(student3);

        // 计算晴雨TS评分
        RainNoRainTSResult result = calculateRainNoRainTSDirectly(studentData, actualData);

        // 验证微量降水特殊处理
        // 期望：A=2（微量降水预报无雨和小雨都算正确），B=1（微量降水预报中雨算空报），C=0，D=0
        if (result.A != 2) {
            throw new AssertionError("微量降水特殊处理：正确预报数应该为2，实际为：" + result.A);
        }
        if (result.B != 1) {
            throw new AssertionError("微量降水特殊处理：空报数应该为1，实际为：" + result.B);
        }

        System.out.println("微量降水特殊处理测试通过");
        System.out.println("  微量降水预报无雨：算正确A");
        System.out.println("  微量降水预报小雨：算正确A");
        System.out.println("  微量降水预报中雨：算空报B");
    }

    /**
     * 直接计算晴雨TS评分（复制修复后的逻辑）
     */
    private RainNoRainTSResult calculateRainNoRainTSDirectly(List<StationPrecipitationData> forecastData,
                                                            List<StationPrecipitationData> actualData) {
        int A = 0, B = 0, C = 0, D = 0;

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);

            String actualLevel = actual.getActualLevel();
            String forecastLevel = forecast.getForecastLevel();

            // 微量降水特殊处理：实况为微量降水时，预报无雨、小雨或微量降水都算正确
            if ("微量降水".equals(actualLevel)) {
                if ("无雨".equals(forecastLevel) || "小雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
                    A++; // 正确预报有降水（微量降水特殊规则）
                } else {
                    B++; // 空报（预报为中雨及以上）
                }
            } else {
                // 常规晴雨评分逻辑
                boolean actualRain = actual.hasActualRain();
                boolean forecastRain = forecast.hasForecastRain();

                if (forecastRain && actualRain) {
                    A++; // 正确预报有降水
                } else if (forecastRain && !actualRain) {
                    B++; // 空报
                } else if (!forecastRain && actualRain) {
                    C++; // 漏报
                } else {
                    D++; // 正确预报无降水
                }
            }
        }

        int total = A + B + C + D;
        double tsScore = total > 0 ? (double)(A + D) / total : 0.0;

        System.out.println(String.format("晴雨TS评分详情：A=%d, B=%d, C=%d, D=%d, Total=%d, TS=%.3f",
            A, B, C, D, total, tsScore));

        return new RainNoRainTSResult(A, B, C, D, tsScore);
    }

    /**
     * 创建测试用的实况数据
     */
    private List<StationPrecipitationData> createTestActualData() {
        List<StationPrecipitationData> actualData = new ArrayList<>();
        
        // 有雨站点2个
        actualData.add(createStation(1, 5.0, "小雨"));
        actualData.add(createStation(2, 15.0, "中雨"));
        
        // 无雨站点2个
        actualData.add(createStation(3, 0.0, "无雨"));
        actualData.add(createStation(4, 0.0, "无雨"));
        
        // 有雨但会被漏报的站点1个
        actualData.add(createStation(5, 8.0, "小雨"));
        
        // 无雨但会被空报的站点1个
        actualData.add(createStation(6, 0.0, "无雨"));
        
        return actualData;
    }

    /**
     * 创建测试用的考生预报数据
     */
    private List<StationPrecipitationData> createTestStudentData() {
        List<StationPrecipitationData> studentData = new ArrayList<>();
        
        // 正确预报有雨2个（A=2）
        studentData.add(createStation(1, 6.0, "小雨"));   // 实况小雨，预报小雨
        studentData.add(createStation(2, 16.0, "中雨"));  // 实况中雨，预报中雨
        
        // 正确预报无雨2个（D=2）
        studentData.add(createStation(3, 0.0, "无雨"));   // 实况无雨，预报无雨
        studentData.add(createStation(4, 0.0, "无雨"));   // 实况无雨，预报无雨
        
        // 漏报1个（C=1）
        studentData.add(createStation(5, 0.0, "无雨"));   // 实况小雨，预报无雨
        
        // 空报1个（B=1）
        studentData.add(createStation(6, 7.0, "小雨"));   // 实况无雨，预报小雨
        
        return studentData;
    }

    /**
     * 创建站点数据
     */
    private StationPrecipitationData createStation(int id, double precipitation, String level) {
        StationPrecipitationData data = new StationPrecipitationData();
        data.setStationId(50000 + id);
        data.setLongitude(110.0 + id * 0.1);
        data.setLatitude(30.0 + id * 0.1);
        data.setActualPrecipitation(precipitation);
        data.setActualLevel(level);
        data.setForecastPrecipitation(precipitation);
        data.setForecastLevel(level);
        return data;
    }

    /**
     * 晴雨TS评分结果
     */
    private static class RainNoRainTSResult {
        public final int A, B, C, D;
        public final double tsScore;

        public RainNoRainTSResult(int A, int B, int C, int D, double tsScore) {
            this.A = A;
            this.B = B;
            this.C = C;
            this.D = D;
            this.tsScore = tsScore;
        }
    }
}
