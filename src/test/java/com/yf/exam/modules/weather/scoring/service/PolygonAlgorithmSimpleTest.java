package com.yf.exam.modules.weather.scoring.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简单的多边形算法测试
 * 验证新的多边形内点判断算法是否正常工作
 */
@SpringBootTest
@ActiveProfiles("test")
public class PolygonAlgorithmSimpleTest {

    @Test
    void testPolygonAlgorithmConfiguration() {
        // 创建服务实例
        PrecipitationAreaScoringService service = new PrecipitationAreaScoringService();
        
        // 测试算法配置
        service.setPointInPolygonAlgorithm("ray_casting");
        service.setPointInPolygonAlgorithm("winding_number");
        service.setPointInPolygonAlgorithm("multi_method");
        
        // 测试容差配置
        service.setPolygonTolerances(0.01, 0.005);
        service.setPolygonTolerances(0.02, 0.01);
        service.setPolygonTolerances(0.05, 0.02);
        
        System.out.println("多边形算法配置测试通过");
    }
    
    @Test
    void testStudentAnswerStructure() {
        // 测试考生答案数据结构
        Map<String, Object> studentAnswer = createTestStudentAnswer();
        
        // 验证数据结构
        assert studentAnswer.containsKey("content");
        Map<String, Object> content = (Map<String, Object>) studentAnswer.get("content");
        assert content.containsKey("areas");
        Map<String, Object> areas = (Map<String, Object>) content.get("areas");
        assert areas.containsKey("level0"); // 小雨
        
        System.out.println("考生答案数据结构测试通过");
    }
    
    /**
     * 创建测试用的考生答案数据
     */
    private Map<String, Object> createTestStudentAnswer() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> areas = new HashMap<>();
        
        // 创建小雨区域（level0）
        List<Map<String, Object>> lightRainAreas = Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(116.0, 39.0),
                Arrays.asList(118.0, 39.0),
                Arrays.asList(118.0, 41.0),
                Arrays.asList(116.0, 41.0),
                Arrays.asList(116.0, 39.0)
            ))
        );
        
        // 创建中雨区域（level1）
        List<Map<String, Object>> moderateRainAreas = Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(117.0, 39.5),
                Arrays.asList(117.5, 39.5),
                Arrays.asList(117.5, 40.5),
                Arrays.asList(117.0, 40.5),
                Arrays.asList(117.0, 39.5)
            ))
        );
        
        areas.put("level0", lightRainAreas);   // 小雨
        areas.put("level1", moderateRainAreas); // 中雨
        
        content.put("areas", areas);
        content.put("region", "region1");
        content.put("totalCount", 2);
        content.put("version", "1.0");
        
        answer.put("content", content);
        answer.put("createTime", "2025-08-05T10:00:00.000Z");
        
        return answer;
    }
    
    /**
     * 创建多边形区域
     */
    private Map<String, Object> createPolygonArea(List<List<Double>> coordinates) {
        Map<String, Object> area = new HashMap<>();
        Map<String, Object> geometry = new HashMap<>();
        
        geometry.put("type", "Polygon");
        geometry.put("coordinates", Arrays.asList(coordinates));
        
        area.put("geometry", geometry);
        area.put("properties", new HashMap<>());
        
        return area;
    }
    
    @Test
    void testImperfectPolygonHandling() {
        // 测试不完美闭合的多边形
        Map<String, Object> imperfectAnswer = createImperfectPolygonAnswer();
        
        // 验证数据结构
        Map<String, Object> content = (Map<String, Object>) imperfectAnswer.get("content");
        Map<String, Object> areas = (Map<String, Object>) content.get("areas");
        List<Map<String, Object>> level0Areas = (List<Map<String, Object>>) areas.get("level0");
        
        assert level0Areas != null && !level0Areas.isEmpty();
        
        Map<String, Object> area = level0Areas.get(0);
        Map<String, Object> geometry = (Map<String, Object>) area.get("geometry");
        List<List<List<Double>>> coordinates = (List<List<List<Double>>>) geometry.get("coordinates");
        List<List<Double>> polygon = coordinates.get(0);
        
        // 验证多边形有足够的点
        assert polygon.size() >= 4;
        
        // 验证第一个点和最后一个点不完全相同（模拟手绘尾巴）
        List<Double> first = polygon.get(0);
        List<Double> last = polygon.get(polygon.size() - 1);
        
        double distance = Math.sqrt(
            Math.pow(first.get(0) - last.get(0), 2) + 
            Math.pow(first.get(1) - last.get(1), 2)
        );
        
        // 应该有小的距离差（模拟手绘尾巴）
        assert distance > 0 && distance < 0.1;
        
        System.out.println("不完美多边形处理测试通过，尾巴距离: " + distance + "度");
    }
    
    /**
     * 创建包含不完美闭合的多边形答案
     */
    private Map<String, Object> createImperfectPolygonAnswer() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> areas = new HashMap<>();
        
        // 创建不完美闭合的多边形（最后一点与第一点有小偏差）
        List<Map<String, Object>> imperfectAreas = Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(116.0, 39.0),
                Arrays.asList(118.0, 39.0),
                Arrays.asList(118.0, 41.0),
                Arrays.asList(116.0, 41.0),
                Arrays.asList(116.005, 39.003)  // 小尾巴：距离第一点约0.5km
            ))
        );
        
        areas.put("level0", imperfectAreas);
        content.put("areas", areas);
        answer.put("content", content);
        
        return answer;
    }
    
    @Test
    void testComplexPolygonHandling() {
        // 测试复杂多边形
        Map<String, Object> complexAnswer = createComplexPolygonAnswer();
        
        Map<String, Object> content = (Map<String, Object>) complexAnswer.get("content");
        Map<String, Object> areas = (Map<String, Object>) content.get("areas");
        List<Map<String, Object>> level5Areas = (List<Map<String, Object>>) areas.get("level5");
        
        assert level5Areas != null && !level5Areas.isEmpty();
        
        System.out.println("复杂多边形处理测试通过");
    }
    
    /**
     * 创建复杂多边形答案
     */
    private Map<String, Object> createComplexPolygonAnswer() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> areas = new HashMap<>();
        
        // 创建复杂的凹多边形
        List<Map<String, Object>> complexAreas = Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(116.0, 39.0),
                Arrays.asList(117.0, 39.0),
                Arrays.asList(117.0, 39.5),
                Arrays.asList(116.5, 39.5),  // 凹进去
                Arrays.asList(116.5, 40.5),
                Arrays.asList(117.0, 40.5),
                Arrays.asList(117.0, 41.0),
                Arrays.asList(116.0, 41.0),
                Arrays.asList(116.0, 39.0)
            ))
        );
        
        areas.put("level5", complexAreas);  // level5 对应大雨
        content.put("areas", areas);
        answer.put("content", content);
        
        return answer;
    }
    
    @Test
    void testAlgorithmPerformance() {
        // 简单的性能测试
        PrecipitationAreaScoringService service = new PrecipitationAreaScoringService();
        
        String[] algorithms = {"ray_casting", "winding_number", "multi_method"};
        
        for (String algorithm : algorithms) {
            service.setPointInPolygonAlgorithm(algorithm);
            
            long startTime = System.currentTimeMillis();
            
            // 模拟一些配置操作
            for (int i = 0; i < 100; i++) {
                service.setPolygonTolerances(0.01 + i * 0.001, 0.005 + i * 0.0005);
            }
            
            long endTime = System.currentTimeMillis();
            
            System.out.println(algorithm + " 算法配置性能测试完成，耗时: " + (endTime - startTime) + "ms");
        }
    }
}
