package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.weather.scoring.dto.LevelTSScoringDetail;

import java.util.*;

/**
 * MESO晴雨TS评分详情集成验证测试
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class MesoIntegrationVerificationTest {

    public static void main(String[] args) {
        System.out.println("开始验证MESO详情集成功能...");
        
        try {
            // 创建测试数据
            List<LevelTSScoringDetail> levelTSDetails = createTestLevelDetails();
            Map<String, Object> mesoRainNoRainDetails = createTestMesoRainNoRainDetails();
            Map<String, Object> mesoLevelTSDetails = createTestMesoLevelDetails();
            
            // 创建服务实例
            PrecipitationAreaScoringService service = new PrecipitationAreaScoringService();
            
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = PrecipitationAreaScoringService.class
                .getDeclaredMethod("integrateMesoDetailsIntoLevelScoring", 
                    List.class, Map.class, Map.class);
            method.setAccessible(true);
            method.invoke(service, levelTSDetails, mesoRainNoRainDetails, mesoLevelTSDetails);
            
            // 验证集成结果
            verifyIntegrationResults(levelTSDetails, mesoRainNoRainDetails, mesoLevelTSDetails);
            
            System.out.println("✅ MESO详情集成功能验证通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 集成验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static List<LevelTSScoringDetail> createTestLevelDetails() {
        List<LevelTSScoringDetail> details = new ArrayList<>();
        
        // 创建晴雨量级详情
        LevelTSScoringDetail rainNoRainDetail = new LevelTSScoringDetail();
        rainNoRainDetail.setLevel("晴雨");
        rainNoRainDetail.setTotalStations(100);
        rainNoRainDetail.setStudentTSStats(new LevelTSScoringDetail.TSStatistics());
        rainNoRainDetail.setCmaMesoTSStats(new LevelTSScoringDetail.TSStatistics());
        details.add(rainNoRainDetail);
        
        // 创建小雨量级详情
        LevelTSScoringDetail lightRainDetail = new LevelTSScoringDetail();
        lightRainDetail.setLevel("小雨");
        lightRainDetail.setTotalStations(25);
        lightRainDetail.setSpecialRuleNote("包含微量降水特殊规则");
        lightRainDetail.setStudentTSStats(new LevelTSScoringDetail.TSStatistics());
        lightRainDetail.setCmaMesoTSStats(new LevelTSScoringDetail.TSStatistics());
        details.add(lightRainDetail);
        
        return details;
    }
    
    private static Map<String, Object> createTestMesoRainNoRainDetails() {
        Map<String, Object> details = new HashMap<>();
        details.put("correctA", 65);
        details.put("correctD", 5);
        details.put("wrongB", 20);
        details.put("missedC", 10);
        details.put("total", 100);
        details.put("tsScore", 0.700);
        details.put("summary", "meso晴雨TS评分：正确预报有雨65个，正确预报无雨5个，空报20个，漏报10个，TS评分0.700");
        details.put("formula", "TS = (65 + 5) / (65 + 5 + 20 + 10) = 0.700");
        return details;
    }
    
    private static Map<String, Object> createTestMesoLevelDetails() {
        Map<String, Object> details = new HashMap<>();
        
        // 小雨量级MESO详情
        Map<String, Object> lightRainDetail = new HashMap<>();
        lightRainDetail.put("level", "小雨");
        lightRainDetail.put("correctA", 15);
        lightRainDetail.put("wrongB", 6);
        lightRainDetail.put("missedC", 4);
        lightRainDetail.put("total", 25);
        lightRainDetail.put("tsScore", 0.600);
        lightRainDetail.put("summary", "meso 小雨 TS评分：正确15个，错误6个，漏报4个，TS评分0.600");
        lightRainDetail.put("formula", "TS = 15 / (15 + 6 + 4) = 0.600");
        
        details.put("小雨", lightRainDetail);
        return details;
    }
    
    private static void verifyIntegrationResults(List<LevelTSScoringDetail> levelTSDetails,
                                               Map<String, Object> mesoRainNoRainDetails,
                                               Map<String, Object> mesoLevelTSDetails) {
        
        // 验证晴雨量级的集成结果
        LevelTSScoringDetail rainNoRainDetail = levelTSDetails.stream()
            .filter(detail -> "晴雨".equals(detail.getLevel()))
            .findFirst()
            .orElse(null);
        
        if (rainNoRainDetail == null) {
            throw new RuntimeException("晴雨量级详情不应为空");
        }
        if (rainNoRainDetail.getMesoRainNoRainDetails() == null) {
            throw new RuntimeException("MESO晴雨TS评分详情应已集成");
        }
        if (!rainNoRainDetail.getSpecialRuleNote().contains("MESO晴雨TS评分详情已集成")) {
            throw new RuntimeException("特殊说明应包含MESO集成信息");
        }
        
        System.out.println("✓ 晴雨量级集成验证通过");
        System.out.println("  - 特殊说明: " + rainNoRainDetail.getSpecialRuleNote());
        System.out.println("  - MESO详情已存储: " + (rainNoRainDetail.getMesoRainNoRainDetails() != null));
        
        // 验证小雨量级的集成结果
        LevelTSScoringDetail lightRainDetail = levelTSDetails.stream()
            .filter(detail -> "小雨".equals(detail.getLevel()))
            .findFirst()
            .orElse(null);
        
        if (lightRainDetail == null) {
            throw new RuntimeException("小雨量级详情不应为空");
        }
        if (lightRainDetail.getMesoLevelDetails() == null) {
            throw new RuntimeException("MESO小雨TS评分详情应已集成");
        }
        if (!lightRainDetail.getSpecialRuleNote().contains("MESO小雨TS评分详情已集成")) {
            throw new RuntimeException("特殊说明应包含MESO集成信息");
        }
        if (!lightRainDetail.getSpecialRuleNote().contains("包含微量降水特殊规则")) {
            throw new RuntimeException("原有的特殊规则说明应保留");
        }
        
        System.out.println("✓ 小雨量级集成验证通过");
        System.out.println("  - 特殊说明: " + lightRainDetail.getSpecialRuleNote());
        System.out.println("  - MESO详情已存储: " + (lightRainDetail.getMesoLevelDetails() != null));
        System.out.println("  - 原有规则保留: " + lightRainDetail.getSpecialRuleNote().contains("包含微量降水特殊规则"));
    }
}
