<template>
  <div class="convection-exam-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>强对流考试管理</h2>
        <div class="header-desc">管理强对流天气临近预报考试的发布和组织</div>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          创建考试
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <i class="el-icon-document" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalExams || 0 }}</div>
              <div class="stat-label">总考试数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon published">
              <i class="el-icon-success" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.publishedExams || 0 }}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon ongoing">
              <i class="el-icon-loading" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.ongoingExams || 0 }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <i class="el-icon-check" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completedExams || 0 }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="考试标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入考试标题"
            clearable
            style="width: 200px;"
          />
        </el-form-item>

        <el-form-item label="考试状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px;"
          >
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="已结束" value="ended" />
          </el-select>
        </el-form-item>

        <el-form-item label="考试时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 300px;"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 考试列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="title" label="考试标题" min-width="200">
          <template slot-scope="scope">
            <div class="exam-title">
              <div class="title-text">{{ scope.row.title }}</div>
              <div class="title-meta">
                <el-tag size="mini" type="info">ID: {{ scope.row.id }}</el-tag>
                <el-tag
                  v-if="scope.row.isTemplate"
                  size="mini"
                  type="warning"
                >
                  模板
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="关联试题" width="120" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.questionCount > 0 ? 'success' : 'danger'"
              size="mini"
            >
              {{ scope.row.questionCount || 0 }}道题目
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="考试时间" width="160" align="center">
          <template slot-scope="scope">
            <div class="exam-time">
              <div class="time-info">
                <i class="el-icon-time" />
                <span>{{ scope.row.duration || 120 }}分钟</span>
              </div>
              <div class="datetime-info">
                {{ formatDateTime(scope.row.startTime) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="参与情况" width="120" align="center">
          <template slot-scope="scope">
            <div class="participant-info">
              <div class="participant-count">
                <span class="submitted">{{ scope.row.submittedCount || 0 }}</span>
                <span class="separator">/</span>
                <span class="total">{{ scope.row.participantCount || 0 }}</span>
              </div>
              <div class="participant-label">已提交/总人数</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              v-if="scope.row.status === 'draft'"
              type="text"
              size="small"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="scope.row.status === 'draft'"
              type="text"
              size="small"
              style="color: #67C23A;"
              icon="el-icon-upload"
              @click="handlePublish(scope.row)"
            >
              发布
            </el-button>
            <el-button
              v-if="scope.row.status === 'published' || scope.row.status === 'ongoing'"
              type="text"
              size="small"
              icon="el-icon-data-analysis"
              @click="handleResults(scope.row)"
            >
              成绩
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, scope.row)">
              <el-button type="text" size="small">
                更多<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="copy">复制</el-dropdown-item>
                <el-dropdown-item v-if="!scope.row.isTemplate" command="template">设为模板</el-dropdown-item>
                <el-dropdown-item command="participants">参与人员</el-dropdown-item>
                <el-dropdown-item command="export">导出数据</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 'draft'"
                  command="delete"
                  divided
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <div class="batch-info">
          <span>已选择 {{ selectedRows.length }} 项</span>
        </div>
        <div class="batch-buttons">
          <el-button size="small" @click="handleBatchPublish">
            批量发布
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            批量删除
          </el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 考试创建/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <div class="exam-form">
        <el-form
          ref="examForm"
          v-loading="formLoading"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>

            <el-form-item label="考试标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入考试标题"
                :readonly="isViewMode"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="考试描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入考试描述，包括考试目的、要求等"
                :readonly="isViewMode"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="考试时长" prop="duration">
                  <el-input-number
                    v-model="formData.duration"
                    :min="30"
                    :max="300"
                    :step="10"
                    :readonly="isViewMode"
                    style="width: 100%;"
                  />
                  <span style="margin-left: 8px; color: #909399;">分钟</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总分" prop="totalScore">
                  <el-input-number
                    v-model="formData.totalScore"
                    :min="50"
                    :max="200"
                    :step="10"
                    :readonly="isViewMode"
                    style="width: 100%;"
                  />
                  <span style="margin-left: 8px; color: #909399;">分</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 试题配置 -->
          <div class="form-section">
            <h4 class="section-title">试题配置</h4>

            <el-form-item label="关联试题" prop="questionIds">
              <div class="question-selector">
                <el-button
                  v-if="!isViewMode"
                  type="primary"
                  icon="el-icon-plus"
                  @click="showQuestionSelector = true"
                >
                  选择试题
                </el-button>

                <div v-if="selectedQuestions.length > 0" class="selected-questions">
                  <h5>已选择试题：</h5>
                  <div class="question-list">
                    <div
                      v-for="question in selectedQuestions"
                      :key="question.id"
                      class="question-item"
                    >
                      <div class="question-info">
                        <span class="question-title">{{ question.title }}</span>
                        <span class="question-time">{{ question.estimatedTime }}分钟</span>
                      </div>
                      <div v-if="!isViewMode" class="question-actions">
                        <el-button
                          type="text"
                          size="mini"
                          style="color: #F56C6C;"
                          icon="el-icon-delete"
                          @click="removeQuestion(question.id)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="selectedQuestions.length === 0" class="empty-questions">
                  <el-empty
                    description="暂未选择试题"
                    :image-size="60"
                  />
                </div>
              </div>
            </el-form-item>
          </div>

          <!-- 时间设置 -->
          <div class="form-section">
            <h4 class="section-title">时间设置</h4>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" prop="startTime">
                  <el-date-picker
                    v-model="formData.startTime"
                    type="datetime"
                    placeholder="选择开始时间"
                    :readonly="isViewMode"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker
                    v-model="formData.endTime"
                    type="datetime"
                    placeholder="选择结束时间"
                    :readonly="isViewMode"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="考试设置">
              <el-checkbox-group v-model="formData.settings" :disabled="isViewMode">
                <el-checkbox label="allowLateEntry">允许迟到进入</el-checkbox>
                <el-checkbox label="showResultImmediately">考试结束后立即显示结果</el-checkbox>
                <el-checkbox label="allowRetake">允许重新考试</el-checkbox>
                <el-checkbox label="randomQuestionOrder">随机试题顺序</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <!-- 参与人员 -->
          <div v-if="!isCreateMode" class="form-section">
            <h4 class="section-title">参与人员</h4>

            <div class="participant-config">
              <div v-if="!isViewMode" class="config-actions">
                <el-button
                  type="primary"
                  size="small"
                  icon="el-icon-plus"
                  @click="showParticipantSelector = true"
                >
                  添加参与人员
                </el-button>
                <el-button
                  type="default"
                  size="small"
                  icon="el-icon-upload"
                  @click="importParticipants"
                >
                  批量导入
                </el-button>
              </div>

              <div v-if="formData.participants && formData.participants.length > 0" class="participant-list">
                <el-table
                  :data="formData.participants"
                  size="small"
                  style="margin-top: 15px;"
                >
                  <el-table-column prop="name" label="姓名" width="100" />
                  <el-table-column prop="username" label="用户名" width="120" />
                  <el-table-column prop="department" label="部门" width="150" />
                  <el-table-column prop="position" label="职位" width="120" />
                  <el-table-column label="状态" width="80" align="center">
                    <template slot-scope="scope">
                      <el-tag
                        :type="scope.row.status === 'submitted' ? 'success' : 'info'"
                        size="mini"
                      >
                        {{ scope.row.status === 'submitted' ? '已提交' : '未开始' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="!isViewMode" label="操作" width="80" align="center">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="mini"
                        style="color: #F56C6C;"
                        @click="removeParticipant(scope.$index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div v-else class="empty-participants">
                <el-empty
                  description="暂无参与人员"
                  :image-size="60"
                />
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!isViewMode"
          :loading="saving"
          @click="handleSaveDraft"
        >
          保存草稿
        </el-button>
        <el-button
          v-if="!isViewMode"
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 试题选择对话框 -->
    <el-dialog
      title="选择试题"
      :visible.sync="showQuestionSelector"
      width="800px"
    >
      <div class="question-selector-content">
        <el-table
          v-loading="questionLoading"
          :data="availableQuestions"
          @selection-change="handleQuestionSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="title" label="试题标题" min-width="200" />
          <el-table-column prop="description" label="描述" min-width="150" />
          <el-table-column prop="estimatedTime" label="预计用时" width="100" align="center">
            <template slot-scope="scope">
              {{ scope.row.estimatedTime }}分钟
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showQuestionSelector = false">取消</el-button>
        <el-button type="primary" @click="confirmQuestionSelection">确定</el-button>
      </div>
    </el-dialog>

    <!-- 参与人员选择对话框 -->
    <el-dialog
      title="选择参与人员"
      :visible.sync="showParticipantSelector"
      width="800px"
    >
      <div class="participant-selector-content">
        <!-- 搜索区域 -->
        <div class="search-bar">
          <el-input
            v-model="participantSearch"
            placeholder="搜索用户姓名或用户名"
            prefix-icon="el-icon-search"
            style="width: 300px;"
            @input="searchParticipants"
          />
        </div>

        <!-- 用户列表 -->
        <el-table
          v-loading="participantLoading"
          :data="availableParticipants"
          style="margin-top: 15px;"
          @selection-change="handleParticipantSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="department" label="部门" width="150" />
          <el-table-column prop="position" label="职位" width="120" />
          <el-table-column prop="email" label="邮箱" min-width="180" />
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showParticipantSelector = false">取消</el-button>
        <el-button type="primary" @click="confirmParticipantSelection">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { convectionApi } from '@/api/convection/convection'

export default {
  name: 'ConvectionExamManage',

  data() {
    return {
      // 统计数据
      statistics: {
        totalExams: 0,
        publishedExams: 0,
        ongoingExams: 0,
        completedExams: 0
      },

      // 搜索表单
      searchForm: {
        title: '',
        status: '',
        dateRange: []
      },

      // 表格数据
      tableData: [],
      selectedRows: [],
      loading: false,

      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },

      // 对话框
      dialogVisible: false,
      dialogMode: 'create', // create, edit, view
      formLoading: false,
      saving: false,

      // 表单数据
      formData: {
        id: null,
        title: '',
        description: '',
        duration: 120,
        totalScore: 100,
        questionIds: [],
        startTime: null,
        endTime: null,
        settings: [],
        participants: []
      },

      // 试题选择
      showQuestionSelector: false,
      availableQuestions: [],
      selectedQuestions: [],
      questionLoading: false,
      tempSelectedQuestions: [],

      // 参与人员选择
      showParticipantSelector: false,
      availableParticipants: [],
      participantLoading: false,
      participantSearch: '',
      tempSelectedParticipants: [],

      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入考试标题', trigger: 'blur' },
          { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入考试描述', trigger: 'blur' }
        ],
        duration: [
          { required: true, message: '请设置考试时长', trigger: 'blur' }
        ],
        totalScore: [
          { required: true, message: '请设置总分', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    dialogTitle() {
      const titleMap = {
        create: '创建考试',
        edit: '编辑考试',
        view: '查看考试'
      }
      return titleMap[this.dialogMode] || '考试管理'
    },

    isViewMode() {
      return this.dialogMode === 'view'
    },

    isEditMode() {
      return this.dialogMode === 'edit'
    },

    isCreateMode() {
      return this.dialogMode === 'create'
    }
  },

  watch: {
    showQuestionSelector(visible) {
      if (visible) {
        this.loadAvailableQuestions()
      }
    },

    showParticipantSelector(visible) {
      if (visible) {
        this.loadAvailableParticipants()
      }
    }
  },

  created() {
    this.loadStatistics()
    this.loadTableData()
  },

  methods: {
    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await convectionApi.getExamStatistics()
        this.statistics = response.data || {}
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          size: this.pagination.pageSize,
          title: this.searchForm.title,
          status: this.searchForm.status,
          startTime: this.searchForm.dateRange?.[0],
          endTime: this.searchForm.dateRange?.[1]
        }

        const response = await convectionApi.getExamList(params)

        this.tableData = response.data.records || response.data.list || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        console.error('加载考试列表失败:', error)
        this.$message.error('加载考试列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        status: '',
        dateRange: []
      }
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadTableData()
    },

    // 选择变化
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },

    // 创建考试
    handleCreate() {
      this.dialogMode = 'create'
      this.resetFormData()
      this.dialogVisible = true
    },

    // 查看考试
    async handleView(row) {
      this.dialogMode = 'view'
      await this.loadExamDetail(row.id)
      this.dialogVisible = true
    },

    // 编辑考试
    async handleEdit(row) {
      this.dialogMode = 'edit'
      await this.loadExamDetail(row.id)
      this.dialogVisible = true
    },

    // 发布考试
    async handlePublish(row) {
      this.$confirm('确定要发布这个考试吗？发布后将无法修改基本信息。', '确认发布', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await convectionApi.publishExam(row.id)
          this.$message.success('考试发布成功')
          this.loadTableData()
          this.loadStatistics()
        } catch (error) {
          this.$message.error('考试发布失败')
          console.error(error)
        }
      })
    },

    // 查看成绩
    handleResults(row) {
      this.$router.push({
        name: 'ConvectionExamResults',
        params: { examId: row.id }
      })
    },

    // 下拉菜单操作
    async handleDropdownCommand(command, row) {
      switch (command) {
        case 'copy':
          await this.copyExam(row)
          break
        case 'template':
          await this.setAsTemplate(row)
          break
        case 'participants':
          this.manageParticipants(row)
          break
        case 'export':
          this.exportExamData(row)
          break
        case 'delete':
          this.deleteExam(row)
          break
      }
    },

    // 复制考试
    async copyExam(row) {
      try {
        await this.loadExamDetail(row.id)

        // 重置ID和标题
        this.formData.id = null
        this.formData.title = `${this.formData.title} - 副本`
        this.formData.startTime = null
        this.formData.endTime = null
        this.formData.participants = []

        this.dialogMode = 'create'
        this.dialogVisible = true
      } catch (error) {
        this.$message.error('复制考试失败')
        console.error(error)
      }
    },

    // 设为模板
    async setAsTemplate(row) {
      this.$confirm('确定要将此考试设为模板吗？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async() => {
        try {
          await convectionApi.setExamAsTemplate(row.id)
          this.$message.success('已设为模板')
          this.loadTableData()
        } catch (error) {
          this.$message.error('操作失败')
          console.error(error)
        }
      })
    },

    // 管理参与人员
    manageParticipants(row) {
      this.$router.push({
        name: 'ConvectionExamParticipants',
        params: { examId: row.id }
      })
    },

    // 导出考试数据
    exportExamData(row) {
      // 实现导出功能
      this.$message.info('导出功能开发中...')
    },

    // 删除考试
    deleteExam(row) {
      this.$confirm('确定要删除这个考试吗？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await convectionApi.deleteExam(row.id)
          this.$message.success('考试删除成功')
          this.loadTableData()
          this.loadStatistics()
        } catch (error) {
          this.$message.error('考试删除失败')
          console.error(error)
        }
      })
    },

    // 批量发布
    handleBatchPublish() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要发布的考试')
        return
      }

      const draftExams = this.selectedRows.filter(row => row.status === 'draft')
      if (draftExams.length === 0) {
        this.$message.warning('只能发布草稿状态的考试')
        return
      }

      this.$confirm(`确定要批量发布选中的${draftExams.length}个考试吗？`, '确认批量发布', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async() => {
        try {
          const promises = draftExams.map(row => convectionApi.publishExam(row.id))
          await Promise.all(promises)
          this.$message.success('批量发布成功')
          this.loadTableData()
          this.loadStatistics()
        } catch (error) {
          this.$message.error('批量发布失败')
          console.error(error)
        }
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的考试')
        return
      }

      const draftExams = this.selectedRows.filter(row => row.status === 'draft')
      if (draftExams.length === 0) {
        this.$message.warning('只能删除草稿状态的考试')
        return
      }

      this.$confirm(`确定要批量删除选中的${draftExams.length}个考试吗？删除后无法恢复。`, '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const promises = draftExams.map(row => convectionApi.deleteExam(row.id))
          await Promise.all(promises)
          this.$message.success('批量删除成功')
          this.loadTableData()
          this.loadStatistics()
        } catch (error) {
          this.$message.error('批量删除失败')
          console.error(error)
        }
      })
    },

    // 加载考试详情
    async loadExamDetail(examId) {
      this.formLoading = true
      try {
        const response = await convectionApi.getExamDetail(examId)
        const data = response.data

        this.formData = {
          id: data.id,
          title: data.title || '',
          description: data.description || '',
          duration: data.duration || 120,
          totalScore: data.totalScore || 100,
          questionIds: data.questionIds || [],
          startTime: data.startTime ? new Date(data.startTime) : null,
          endTime: data.endTime ? new Date(data.endTime) : null,
          settings: data.settings || [],
          participants: data.participants || []
        }

        // 加载关联试题
        if (data.questionIds && data.questionIds.length > 0) {
          await this.loadSelectedQuestions(data.questionIds)
        }
      } catch (error) {
        this.$message.error('加载考试详情失败')
        console.error(error)
      } finally {
        this.formLoading = false
      }
    },

    // 加载已选择的试题
    async loadSelectedQuestions(questionIds) {
      try {
        const response = await convectionApi.getQuestionsByIds(questionIds)
        this.selectedQuestions = response.data || []
      } catch (error) {
        console.error('加载试题详情失败:', error)
      }
    },

    // 加载可选试题
    async loadAvailableQuestions() {
      this.questionLoading = true
      try {
        const response = await convectionApi.getQuestionList({
          page: 1,
          size: 100,
          status: 1 // 只加载启用的试题
        })
        this.availableQuestions = response.data.records || response.data.list || []
      } catch (error) {
        this.$message.error('加载试题列表失败')
        console.error(error)
      } finally {
        this.questionLoading = false
      }
    },

    // 试题选择变化
    handleQuestionSelectionChange(selection) {
      this.tempSelectedQuestions = selection
    },

    // 确认试题选择
    confirmQuestionSelection() {
      // 合并已选择的试题，避免重复
      const existingIds = this.selectedQuestions.map(q => q.id)
      const newQuestions = this.tempSelectedQuestions.filter(q => !existingIds.includes(q.id))

      this.selectedQuestions = [...this.selectedQuestions, ...newQuestions]
      this.formData.questionIds = this.selectedQuestions.map(q => q.id)

      this.showQuestionSelector = false
      this.tempSelectedQuestions = []

      this.$message.success(`已选择${newQuestions.length}道试题`)
    },

    // 删除试题
    removeQuestion(questionId) {
      const index = this.selectedQuestions.findIndex(q => q.id === questionId)
      if (index !== -1) {
        this.selectedQuestions.splice(index, 1)
        this.formData.questionIds = this.selectedQuestions.map(q => q.id)
      }
    },

    // 加载可选参与人员
    async loadAvailableParticipants() {
      this.participantLoading = true
      try {
        const response = await convectionApi.getUserList({
          page: 1,
          size: 100,
          keyword: this.participantSearch
        })
        this.availableParticipants = response.data.records || response.data.list || []
      } catch (error) {
        this.$message.error('加载用户列表失败')
        console.error(error)
      } finally {
        this.participantLoading = false
      }
    },

    // 搜索参与人员
    searchParticipants() {
      this.loadAvailableParticipants()
    },

    // 参与人员选择变化
    handleParticipantSelectionChange(selection) {
      this.tempSelectedParticipants = selection
    },

    // 确认参与人员选择
    confirmParticipantSelection() {
      // 合并已选择的参与人员，避免重复
      const existingIds = this.formData.participants.map(p => p.id)
      const newParticipants = this.tempSelectedParticipants.filter(p => !existingIds.includes(p.id))

      this.formData.participants = [...this.formData.participants, ...newParticipants]

      this.showParticipantSelector = false
      this.tempSelectedParticipants = []

      this.$message.success(`已添加${newParticipants.length}名参与人员`)
    },

    // 删除参与人员
    removeParticipant(index) {
      this.formData.participants.splice(index, 1)
    },

    // 批量导入参与人员
    importParticipants() {
      this.$message.info('批量导入功能开发中...')
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        id: null,
        title: '',
        description: '',
        duration: 120,
        totalScore: 100,
        questionIds: [],
        startTime: null,
        endTime: null,
        settings: [],
        participants: []
      }
      this.selectedQuestions = []

      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.examForm) {
          this.$refs.examForm.clearValidate()
        }
      })
    },

    // 保存草稿
    async handleSaveDraft() {
      try {
        await this.$refs.examForm.validate()

        this.saving = true

        const data = {
          ...this.formData,
          status: 'draft'
        }

        if (this.isEditMode) {
          await convectionApi.updateExam(data.id, data)
          this.$message.success('草稿保存成功')
        } else {
          await convectionApi.createExam(data)
          this.$message.success('草稿创建成功')
        }

        this.dialogVisible = false
        this.loadTableData()
        this.loadStatistics()
      } catch (error) {
        if (error !== false) {
          this.$message.error('保存草稿失败')
          console.error(error)
        }
      } finally {
        this.saving = false
      }
    },

    // 保存考试
    async handleSave() {
      try {
        await this.$refs.examForm.validate()

        if (this.selectedQuestions.length === 0) {
          this.$message.warning('请至少选择一道试题')
          return
        }

        this.saving = true

        const data = {
          ...this.formData,
          status: 'published'
        }

        if (this.isEditMode) {
          await convectionApi.updateExam(data.id, data)
          this.$message.success('考试更新成功')
        } else {
          await convectionApi.createExam(data)
          this.$message.success('考试创建成功')
        }

        this.dialogVisible = false
        this.loadTableData()
        this.loadStatistics()
      } catch (error) {
        if (error !== false) {
          this.$message.error('保存考试失败')
          console.error(error)
        }
      } finally {
        this.saving = false
      }
    },

    // 关闭对话框
    handleDialogClose() {
      if (this.saving) return

      this.dialogVisible = false
      this.resetFormData()
    },

    // 工具方法
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'published': 'success',
        'ongoing': 'warning',
        'ended': 'danger'
      }
      return typeMap[status] || 'info'
    },

    getStatusText(status) {
      const textMap = {
        'draft': '草稿',
        'published': '已发布',
        'ongoing': '进行中',
        'ended': '已结束'
      }
      return textMap[status] || '未知'
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-exam-manage {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: bold;
      }

      .header-desc {
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .stats-section {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: #fff;
        }

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.published {
          background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
        }

        &.ongoing {
          background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
        }
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .stat-label {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }

  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .exam-title {
      .title-text {
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .title-meta {
        display: flex;
        gap: 5px;
        font-size: 12px;
      }
    }

    .exam-time {
      .time-info {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 4px;

        i {
          color: #909399;
        }

        span {
          font-size: 12px;
          color: #606266;
        }
      }

      .datetime-info {
        font-size: 12px;
        color: #909399;
      }
    }

    .participant-info {
      text-align: center;

      .participant-count {
        margin-bottom: 4px;

        .submitted {
          color: #67C23A;
          font-weight: bold;
        }

        .separator {
          margin: 0 4px;
          color: #C0C4CC;
        }

        .total {
          color: #606266;
        }
      }

      .participant-label {
        font-size: 11px;
        color: #909399;
      }
    }

    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: #F8F9FA;
      border-top: 1px solid #E4E7ED;

      .batch-info {
        color: #606266;
        font-size: 14px;
      }

      .batch-buttons {
        display: flex;
        gap: 10px;
      }
    }

    .pagination-section {
      padding: 20px;
      text-align: center;
      border-top: 1px solid #E4E7ED;
    }
  }

  .exam-form {
    .form-section {
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        color: #303133;
        font-size: 16px;
        font-weight: bold;
        border-bottom: 2px solid #E1F0FF;
      }
    }

    .question-selector {
      .selected-questions {
        margin-top: 15px;

        h5 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 14px;
          font-weight: bold;
        }

        .question-list {
          .question-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: #F8F9FA;
            border: 1px solid #E9ECEF;
            border-radius: 6px;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            .question-info {
              flex: 1;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .question-title {
                color: #303133;
                font-weight: bold;
              }

              .question-time {
                color: #909399;
                font-size: 12px;
              }
            }

            .question-actions {
              margin-left: 15px;
            }
          }
        }
      }

      .empty-questions, .empty-participants {
        text-align: center;
        padding: 30px 20px;
        background: #F8F9FA;
        border-radius: 6px;
        margin-top: 15px;
      }
    }

    .participant-config {
      .config-actions {
        margin-bottom: 15px;

        .el-button {
          margin-right: 10px;
        }
      }
    }
  }

  .question-selector-content, .participant-selector-content {
    .search-bar {
      margin-bottom: 15px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .convection-exam-manage {
    .stats-section {
      .el-col {
        margin-bottom: 15px;
      }
    }
  }
}

@media (max-width: 768px) {
  .convection-exam-manage {
    .page-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
    }

    .search-section .search-form {
      .el-form-item {
        margin-bottom: 15px;

        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }

    .stats-section {
      .stat-card {
        flex-direction: column;
        text-align: center;

        .stat-icon {
          margin-right: 0;
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
