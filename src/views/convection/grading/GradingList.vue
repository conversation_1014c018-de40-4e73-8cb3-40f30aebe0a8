<template>
  <div class="grading-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>批卷任务管理</h2>
        <div class="header-desc">管理强对流考试的人工批卷任务分配和进度跟踪</div>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleCreateTask"
        >
          创建批卷任务
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <i class="el-icon-document" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalTasks || 0 }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon pending">
              <i class="el-icon-time" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingTasks || 0 }}</div>
              <div class="stat-label">待批卷</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon grading">
              <i class="el-icon-edit" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.gradingTasks || 0 }}</div>
              <div class="stat-label">批卷中</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <i class="el-icon-check" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completedTasks || 0 }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="考试名称">
          <el-input
            v-model="searchForm.examTitle"
            placeholder="请输入考试名称"
            clearable
            style="width: 200px;"
          />
        </el-form-item>

        <el-form-item label="批卷状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px;"
          >
            <el-option label="待分配" value="pending" />
            <el-option label="批卷中" value="grading" />
            <el-option label="已完成" value="completed" />
            <el-option label="已审核" value="reviewed" />
          </el-select>
        </el-form-item>

        <el-form-item label="批卷教师">
          <el-select
            v-model="searchForm.graderId"
            placeholder="请选择教师"
            clearable
            filterable
            style="width: 150px;"
          >
            <el-option
              v-for="teacher in teacherList"
              :key="teacher.id"
              :label="teacher.name"
              :value="teacher.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px;"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 批卷任务列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="examTitle" label="考试名称" min-width="200">
          <template slot-scope="scope">
            <div class="exam-info">
              <div class="exam-title">{{ scope.row.examTitle }}</div>
              <div class="exam-meta">
                <el-tag size="mini" type="info">{{ scope.row.examId }}</el-tag>
                <span class="exam-time">{{ formatDate(scope.row.examTime) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="答卷统计" width="120" align="center">
          <template slot-scope="scope">
            <div class="answer-stats">
              <div class="stats-number">
                <span class="total">{{ scope.row.totalAnswers || 0 }}</span>
              </div>
              <div class="stats-label">份答卷</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="批卷进度" width="150" align="center">
          <template slot-scope="scope">
            <div class="grading-progress">
              <div class="progress-text">
                <span class="graded">{{ scope.row.gradedCount || 0 }}</span>
                <span class="separator">/</span>
                <span class="total">{{ scope.row.totalAnswers || 0 }}</span>
              </div>
              <el-progress
                :percentage="getProgressPercentage(scope.row)"
                :show-text="false"
                size="small"
                :color="getProgressColor(getProgressPercentage(scope.row))"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="批卷教师" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.graders && scope.row.graders.length > 0" class="grader-info">
              <div
                v-for="(grader, index) in scope.row.graders.slice(0, 2)"
                :key="grader.id"
                class="grader-item"
              >
                <el-avatar :size="24" :src="grader.avatar">{{ grader.name.charAt(0) }}</el-avatar>
                <span class="grader-name">{{ grader.name }}</span>
              </div>
              <div v-if="scope.row.graders.length > 2" class="more-graders">
                +{{ scope.row.graders.length - 2 }}
              </div>
            </div>
            <div v-else class="no-grader">
              <span>未分配</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="平均分" width="100" align="center">
          <template slot-scope="scope">
            <div class="average-score">
              <span v-if="scope.row.averageScore !== null" class="score-value">
                {{ scope.row.averageScore.toFixed(1) }}
              </span>
              <span v-else class="no-score">-</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="deadline" label="截止时间" width="160" align="center">
          <template slot-scope="scope">
            <div class="deadline-info">
              <div class="deadline-date">{{ formatDateTime(scope.row.deadline) }}</div>
              <div
                class="deadline-status"
                :class="{ 'overdue': isOverdue(scope.row.deadline) }"
              >
                {{ getDeadlineStatus(scope.row.deadline) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              v-if="scope.row.status === 'pending'"
              type="text"
              size="small"
              icon="el-icon-user"
              @click="handleAssign(scope.row)"
            >
              分配
            </el-button>
            <el-button
              v-if="scope.row.status !== 'pending'"
              type="text"
              size="small"
              icon="el-icon-edit"
              @click="handleGrade(scope.row)"
            >
              批卷
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, scope.row)">
              <el-button type="text" size="small">
                更多<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="export">导出成绩</el-dropdown-item>
                <el-dropdown-item command="statistics">批卷统计</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status === 'grading'" command="remind">提醒批卷</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 'pending'"
                  command="delete"
                  divided
                >
                  删除任务
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <div class="batch-info">
          <span>已选择 {{ selectedRows.length }} 项</span>
        </div>
        <div class="batch-buttons">
          <el-button size="small" @click="handleBatchAssign">
            批量分配
          </el-button>
          <el-button size="small" @click="handleBatchRemind">
            批量提醒
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            批量删除
          </el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 任务创建对话框 -->
    <el-dialog
      title="创建批卷任务"
      :visible.sync="createDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="create-task-form">
        <el-form
          ref="createForm"
          :model="createForm"
          :rules="createRules"
          label-width="120px"
        >
          <el-form-item label="选择考试" prop="examId">
            <el-select
              v-model="createForm.examId"
              placeholder="请选择考试"
              filterable
              style="width: 100%;"
              @change="handleExamChange"
            >
              <el-option
                v-for="exam in availableExams"
                :key="exam.id"
                :label="`${exam.title} (${formatDate(exam.examTime)})`"
                :value="exam.id"
              >
                <span style="float: left">{{ exam.title }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ exam.participantCount }}人参与
                </span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="任务描述" prop="description">
            <el-input
              v-model="createForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入批卷任务描述"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="截止时间" prop="deadline">
            <el-date-picker
              v-model="createForm.deadline"
              type="datetime"
              placeholder="选择截止时间"
              style="width: 100%;"
            />
          </el-form-item>

          <el-form-item label="分配方式">
            <el-radio-group v-model="createForm.assignType">
              <el-radio label="manual">手动分配</el-radio>
              <el-radio label="auto">自动分配</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="createForm.assignType === 'manual'"
            label="批卷教师"
            prop="graderIds"
          >
            <el-select
              v-model="createForm.graderIds"
              multiple
              placeholder="请选择批卷教师"
              style="width: 100%;"
            >
              <el-option
                v-for="teacher in teacherList"
                :key="teacher.id"
                :label="teacher.name"
                :value="teacher.id"
              >
                <span style="float: left">{{ teacher.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ teacher.department }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="createForm.assignType === 'auto'"
            label="每人批卷数"
          >
            <el-input-number
              v-model="createForm.papersPerGrader"
              :min="1"
              :max="100"
              style="width: 100%;"
            />
            <div class="help-text">
              系统将自动分配批卷任务，每位教师批改约{{ createForm.papersPerGrader }}份答卷
            </div>
          </el-form-item>

          <el-form-item label="特殊要求">
            <el-checkbox-group v-model="createForm.requirements">
              <el-checkbox label="doubleGrading">双重批卷</el-checkbox>
              <el-checkbox label="blindGrading">盲批</el-checkbox>
              <el-checkbox label="crossGrading">交叉批卷</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="creating"
          @click="handleCreateConfirm"
        >
          创建任务
        </el-button>
      </div>
    </el-dialog>

    <!-- 教师分配对话框 -->
    <el-dialog
      title="分配批卷教师"
      :visible.sync="assignDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="assign-form">
        <div class="task-info">
          <h4>任务信息</h4>
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="考试名称">
              {{ currentTask?.examTitle }}
            </el-descriptions-item>
            <el-descriptions-item label="答卷数量">
              {{ currentTask?.totalAnswers }}份
            </el-descriptions-item>
            <el-descriptions-item label="截止时间">
              {{ formatDateTime(currentTask?.deadline) }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(currentTask?.createTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="teacher-selection">
          <h4>选择批卷教师</h4>
          <el-transfer
            v-model="assignForm.selectedTeachers"
            :data="teacherOptions"
            :titles="['可选教师', '已选教师']"
            :button-texts="['移除', '添加']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            filterable
            filter-placeholder="搜索教师"
            style="text-align: left; display: inline-block"
          >
            <template slot-scope="{ option }">
              <div class="teacher-option">
                <div class="teacher-info">
                  <span class="teacher-name">{{ option.label }}</span>
                  <span class="teacher-dept">{{ option.department }}</span>
                </div>
                <div class="teacher-workload">
                  当前任务: {{ option.currentTasks || 0 }}个
                </div>
              </div>
            </template>
          </el-transfer>
        </div>

        <div class="assignment-config">
          <h4>分配配置</h4>
          <el-form :model="assignForm" label-width="120px">
            <el-form-item label="分配方式">
              <el-radio-group v-model="assignForm.assignMethod">
                <el-radio label="average">平均分配</el-radio>
                <el-radio label="custom">自定义分配</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item
              v-if="assignForm.assignMethod === 'custom'"
              label="自定义分配"
            >
              <div class="custom-assignment">
                <div
                  v-for="teacherId in assignForm.selectedTeachers"
                  :key="teacherId"
                  class="assignment-item"
                >
                  <span class="teacher-name">
                    {{ getTeacherName(teacherId) }}:
                  </span>
                  <el-input-number
                    v-model="assignForm.customAssignment[teacherId]"
                    :min="0"
                    :max="currentTask?.totalAnswers || 0"
                    size="small"
                  />
                  <span class="unit">份</span>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="assigning"
          @click="handleAssignConfirm"
        >
          确认分配
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { gradingApi } from '@/api/convection/grading'
import { convectionApi } from '@/api/convection/convection'

export default {
  name: 'GradingList',

  data() {
    return {
      // 统计数据
      statistics: {
        totalTasks: 0,
        pendingTasks: 0,
        gradingTasks: 0,
        completedTasks: 0
      },

      // 搜索表单
      searchForm: {
        examTitle: '',
        status: '',
        graderId: '',
        dateRange: []
      },

      // 表格数据
      tableData: [],
      selectedRows: [],
      loading: false,

      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },

      // 教师列表
      teacherList: [],

      // 创建任务对话框
      createDialogVisible: false,
      creating: false,
      availableExams: [],
      createForm: {
        examId: '',
        description: '',
        deadline: null,
        assignType: 'manual',
        graderIds: [],
        papersPerGrader: 10,
        requirements: []
      },
      createRules: {
        examId: [
          { required: true, message: '请选择考试', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入任务描述', trigger: 'blur' }
        ],
        deadline: [
          { required: true, message: '请选择截止时间', trigger: 'change' }
        ],
        graderIds: [
          { required: true, message: '请选择批卷教师', trigger: 'change' }
        ]
      },

      // 分配对话框
      assignDialogVisible: false,
      assigning: false,
      currentTask: null,
      teacherOptions: [],
      assignForm: {
        selectedTeachers: [],
        assignMethod: 'average',
        customAssignment: {}
      }
    }
  },

  watch: {
    'assignForm.selectedTeachers'(newTeachers) {
      // 当选择的教师变化时，初始化自定义分配
      const customAssignment = {}
      newTeachers.forEach(teacherId => {
        if (!this.assignForm.customAssignment[teacherId]) {
          customAssignment[teacherId] = Math.floor((this.currentTask?.totalAnswers || 0) / newTeachers.length)
        } else {
          customAssignment[teacherId] = this.assignForm.customAssignment[teacherId]
        }
      })
      this.assignForm.customAssignment = customAssignment
    }
  },

  created() {
    this.loadStatistics()
    this.loadTableData()
    this.loadTeacherList()
  },

  methods: {
    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await gradingApi.getGradingStatistics()
        this.statistics = response.data || {}
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          size: this.pagination.pageSize,
          examTitle: this.searchForm.examTitle,
          status: this.searchForm.status,
          graderId: this.searchForm.graderId,
          startDate: this.searchForm.dateRange?.[0],
          endDate: this.searchForm.dateRange?.[1]
        }

        const response = await gradingApi.getGradingTaskList(params)

        this.tableData = response.data.records || response.data.list || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        console.error('加载批卷任务列表失败:', error)
        this.$message.error('加载批卷任务列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载教师列表
    async loadTeacherList() {
      try {
        const response = await gradingApi.getGraderList()
        this.teacherList = response.data || []

        // 转换为 Transfer 组件需要的格式
        this.teacherOptions = this.teacherList.map(teacher => ({
          key: teacher.id,
          label: teacher.name,
          department: teacher.department,
          currentTasks: teacher.currentTasks || 0
        }))
      } catch (error) {
        console.error('加载教师列表失败:', error)
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        examTitle: '',
        status: '',
        graderId: '',
        dateRange: []
      }
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadTableData()
    },

    // 选择变化
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },

    // 创建任务
    async handleCreateTask() {
      // 加载可用考试列表
      await this.loadAvailableExams()
      this.createDialogVisible = true
    },

    // 加载可用考试
    async loadAvailableExams() {
      try {
        const response = await convectionApi.getCompletedExams()
        this.availableExams = response.data || []
      } catch (error) {
        console.error('加载考试列表失败:', error)
      }
    },

    // 考试选择变化
    handleExamChange(examId) {
      const exam = this.availableExams.find(e => e.id === examId)
      if (exam) {
        this.createForm.description = `${exam.title} - 预报依据批卷任务`
      }
    },

    // 确认创建任务
    async handleCreateConfirm() {
      try {
        await this.$refs.createForm.validate()

        this.creating = true

        const data = {
          ...this.createForm
        }

        await gradingApi.createGradingTask(data)

        this.$message.success('批卷任务创建成功')
        this.createDialogVisible = false
        this.resetCreateForm()
        this.loadTableData()
        this.loadStatistics()
      } catch (error) {
        if (error !== false) {
          this.$message.error('创建任务失败')
          console.error(error)
        }
      } finally {
        this.creating = false
      }
    },

    // 重置创建表单
    resetCreateForm() {
      this.createForm = {
        examId: '',
        description: '',
        deadline: null,
        assignType: 'manual',
        graderIds: [],
        papersPerGrader: 10,
        requirements: []
      }

      this.$nextTick(() => {
        if (this.$refs.createForm) {
          this.$refs.createForm.clearValidate()
        }
      })
    },

    // 查看任务
    handleView(row) {
      this.$router.push({
        name: 'GradingTaskDetail',
        params: { taskId: row.id }
      })
    },

    // 分配教师
    handleAssign(row) {
      this.currentTask = row
      this.assignForm = {
        selectedTeachers: [],
        assignMethod: 'average',
        customAssignment: {}
      }
      this.assignDialogVisible = true
    },

    // 确认分配
    async handleAssignConfirm() {
      if (this.assignForm.selectedTeachers.length === 0) {
        this.$message.warning('请至少选择一位批卷教师')
        return
      }

      try {
        this.assigning = true

        const data = {
          taskId: this.currentTask.id,
          graderIds: this.assignForm.selectedTeachers,
          assignMethod: this.assignForm.assignMethod,
          customAssignment: this.assignForm.customAssignment
        }

        await gradingApi.assignGradingTask(data)

        this.$message.success('教师分配成功')
        this.assignDialogVisible = false
        this.loadTableData()
        this.loadStatistics()
      } catch (error) {
        this.$message.error('分配失败')
        console.error(error)
      } finally {
        this.assigning = false
      }
    },

    // 批卷
    handleGrade(row) {
      this.$router.push({
        name: 'ReasoningGrading',
        params: { taskId: row.id }
      })
    },

    // 下拉菜单操作
    async handleDropdownCommand(command, row) {
      switch (command) {
        case 'export':
          this.exportGrades(row)
          break
        case 'statistics':
          this.viewStatistics(row)
          break
        case 'remind':
          this.remindGraders(row)
          break
        case 'delete':
          this.deleteTask(row)
          break
      }
    },

    // 导出成绩
    exportGrades(row) {
      this.$message.info('导出功能开发中...')
    },

    // 查看统计
    viewStatistics(row) {
      this.$router.push({
        name: 'GradingStatistics',
        params: { taskId: row.id }
      })
    },

    // 提醒批卷
    async remindGraders(row) {
      try {
        await gradingApi.remindGraders(row.id)
        this.$message.success('提醒已发送')
      } catch (error) {
        this.$message.error('发送提醒失败')
        console.error(error)
      }
    },

    // 删除任务
    deleteTask(row) {
      this.$confirm('确定要删除这个批卷任务吗？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await gradingApi.deleteGradingTask(row.id)
          this.$message.success('任务删除成功')
          this.loadTableData()
          this.loadStatistics()
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      })
    },

    // 批量分配
    handleBatchAssign() {
      this.$message.info('批量分配功能开发中...')
    },

    // 批量提醒
    handleBatchRemind() {
      this.$message.info('批量提醒功能开发中...')
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的任务')
        return
      }

      const pendingTasks = this.selectedRows.filter(row => row.status === 'pending')
      if (pendingTasks.length === 0) {
        this.$message.warning('只能删除待分配状态的任务')
        return
      }

      this.$confirm(`确定要批量删除选中的${pendingTasks.length}个任务吗？删除后无法恢复。`, '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const promises = pendingTasks.map(row => gradingApi.deleteGradingTask(row.id))
          await Promise.all(promises)
          this.$message.success('批量删除成功')
          this.loadTableData()
          this.loadStatistics()
        } catch (error) {
          this.$message.error('批量删除失败')
          console.error(error)
        }
      })
    },

    // 工具方法
    getProgressPercentage(row) {
      if (!row.totalAnswers || row.totalAnswers === 0) return 0
      return Math.round((row.gradedCount || 0) / row.totalAnswers * 100)
    },

    getProgressColor(percentage) {
      if (percentage >= 80) return '#67C23A'
      if (percentage >= 50) return '#E6A23C'
      return '#F56C6C'
    },

    getStatusTagType(status) {
      const typeMap = {
        'pending': 'info',
        'grading': 'warning',
        'completed': 'success',
        'reviewed': 'primary'
      }
      return typeMap[status] || 'info'
    },

    getStatusText(status) {
      const textMap = {
        'pending': '待分配',
        'grading': '批卷中',
        'completed': '已完成',
        'reviewed': '已审核'
      }
      return textMap[status] || '未知'
    },

    isOverdue(deadline) {
      return new Date(deadline) < new Date()
    },

    getDeadlineStatus(deadline) {
      const now = new Date()
      const deadlineDate = new Date(deadline)
      const diffTime = deadlineDate - now
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays < 0) {
        return '已逾期'
      } else if (diffDays === 0) {
        return '今日截止'
      } else if (diffDays <= 3) {
        return `${diffDays}天后截止`
      } else {
        return '充足时间'
      }
    },

    getTeacherName(teacherId) {
      const teacher = this.teacherList.find(t => t.id === teacherId)
      return teacher ? teacher.name : '未知'
    },

    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.grading-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: bold;
      }

      .header-desc {
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .stats-section {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: #fff;
        }

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
        }

        &.grading {
          background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
        }
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .stat-label {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }

  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .exam-info {
      .exam-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .exam-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;

        .exam-time {
          color: #909399;
        }
      }
    }

    .answer-stats {
      text-align: center;

      .stats-number {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 2px;
      }

      .stats-label {
        font-size: 11px;
        color: #909399;
      }
    }

    .grading-progress {
      .progress-text {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 5px;
        font-size: 12px;

        .graded {
          color: #67C23A;
          font-weight: bold;
        }

        .separator {
          margin: 0 4px;
          color: #C0C4CC;
        }

        .total {
          color: #606266;
        }
      }
    }

    .grader-info {
      .grader-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .grader-name {
          font-size: 12px;
          color: #303133;
        }
      }

      .more-graders {
        font-size: 11px;
        color: #909399;
        text-align: center;
        margin-top: 4px;
      }
    }

    .no-grader {
      text-align: center;
      color: #C0C4CC;
      font-size: 12px;
    }

    .average-score {
      text-align: center;

      .score-value {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .no-score {
        color: #C0C4CC;
      }
    }

    .deadline-info {
      text-align: center;

      .deadline-date {
        font-size: 12px;
        color: #303133;
        margin-bottom: 2px;
      }

      .deadline-status {
        font-size: 11px;
        color: #67C23A;

        &.overdue {
          color: #F56C6C;
        }
      }
    }

    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: #F8F9FA;
      border-top: 1px solid #E4E7ED;

      .batch-info {
        color: #606266;
        font-size: 14px;
      }

      .batch-buttons {
        display: flex;
        gap: 10px;
      }
    }

    .pagination-section {
      padding: 20px;
      text-align: center;
      border-top: 1px solid #E4E7ED;
    }
  }

  .create-task-form, .assign-form {
    .help-text {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }
  }

  .assign-form {
    .task-info {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
        font-weight: bold;
      }
    }

    .teacher-selection {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
        font-weight: bold;
      }

      .teacher-option {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .teacher-info {
          .teacher-name {
            font-weight: bold;
            margin-right: 8px;
          }

          .teacher-dept {
            color: #909399;
            font-size: 12px;
          }
        }

        .teacher-workload {
          font-size: 11px;
          color: #E6A23C;
        }
      }
    }

    .assignment-config {
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
        font-weight: bold;
      }

      .custom-assignment {
        .assignment-item {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 10px;

          .teacher-name {
            min-width: 80px;
            font-size: 13px;
            color: #303133;
          }

          .unit {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .grading-list-container {
    .stats-section {
      .el-col {
        margin-bottom: 15px;
      }
    }
  }
}

@media (max-width: 768px) {
  .grading-list-container {
    .page-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
    }

    .search-section .search-form {
      .el-form-item {
        margin-bottom: 15px;

        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }

    .stats-section {
      .stat-card {
        flex-direction: column;
        text-align: center;

        .stat-icon {
          margin-right: 0;
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
