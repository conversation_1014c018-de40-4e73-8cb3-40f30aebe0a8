<template>
  <div class="convection-question-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>强对流试题管理</h2>
        <div class="header-desc">管理强对流天气临近预报考试试题，包括题目内容、MICAPS数据文件和标准答案</div>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="medium"
          @click="handleCreate"
        >
          新建试题
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="试题标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入试题标题"
            clearable
            style="width: 220px;"
            prefix-icon="el-icon-search"
          />
        </el-form-item>

        <el-form-item label="试题难度">
          <el-select
            v-model="searchForm.difficulty"
            placeholder="请选择难度"
            clearable
            style="width: 140px;"
          >
            <el-option label="简单" value="easy" />
            <el-option label="中等" value="medium" />
            <el-option label="困难" value="hard" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 试题列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />

        <el-table-column prop="title" label="试题标题" min-width="250" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="question-title">
              <div class="title-text">{{ scope.row.title }}</div>
              <div v-if="scope.row.description" class="title-meta">
                {{ scope.row.description }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="MICAPS文件" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.micapsFiles && scope.row.micapsFiles.length > 0 ? 'success' : 'info'"
              size="mini"
            >
              {{ scope.row.micapsFiles ? scope.row.micapsFiles.length : 0 }}个文件
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="落区文件" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.areaFiles && scope.row.areaFiles.length > 0 ? 'success' : 'info'"
              size="mini"
            >
              {{ scope.row.areaFiles ? scope.row.areaFiles.length : 0 }}个文件
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="站点配置" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.stations && scope.row.stations.length > 0 ? 'success' : 'warning'"
              size="mini"
            >
              {{ scope.row.stations ? scope.row.stations.length : 0 }}/4个站点
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="试题难度" width="90" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="getDifficultyTagType(scope.row.difficulty)"
              size="small"
            >
              {{ getDifficultyLabel(scope.row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="140" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140" align="center" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="small"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #F56C6C;"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <div class="batch-info">
          <span>已选择 {{ selectedRows.length }} 项</span>
        </div>
        <div class="batch-buttons">
          <el-button size="small" @click="handleBatchEnable">
            批量启用
          </el-button>
          <el-button size="small" @click="handleBatchDisable">
            批量禁用
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            批量删除
          </el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 试题详情/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="1200px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <div class="question-form">
        <el-form
          ref="questionForm"
          v-loading="formLoading"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>

            <el-form-item label="试题标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入试题标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="试题难度" prop="difficulty">
                  <el-select
                    v-model="formData.difficulty"
                    style="width: 100%;"
                    placeholder="请选择试题难度"
                  >
                    <el-option label="简单" value="easy" />
                    <el-option label="中等" value="medium" />
                    <el-option label="困难" value="hard" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 文件上传区域 -->
          <div class="form-section">
            <h4 class="section-title">文件上传管理</h4>

            <el-row :gutter="20">
              <el-col :span="12">
                <div class="file-upload-area">
                  <h5 class="upload-title">MICAPS气象文件</h5>
                  <el-upload
                    ref="micapsUpload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="uploadData"
                    :file-list="micapsFileList"
                    :on-success="handleMicapsUploadSuccess"
                    :on-error="handleMicapsUploadError"
                    :on-remove="handleMicapsFileRemove"
                    :before-upload="beforeMicapsUpload"
                    multiple
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将MICAPS文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      支持.000、.024等MICAPS格式文件，单个文件不超过50MB
                    </div>
                  </el-upload>
                </div>

                <div v-if="micapsFileList.length > 0" class="file-list">
                  <h5>已上传文件：</h5>
                  <div class="file-items">
                    <div
                      v-for="file in micapsFileList"
                      :key="file.uid || file.id"
                      class="file-item"
                    >
                      <div class="file-info">
                        <i class="el-icon-document" />
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      </div>
                      <div class="file-actions">
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-download"
                          @click="downloadFile(file)"
                        >
                          下载
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          style="color: #F56C6C;"
                          icon="el-icon-delete"
                          @click="removeFile(file)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>

              <el-col :span="12">
                <div class="file-upload-area">
                  <h5 class="upload-title">强对流落区文件</h5>
                  <el-alert
                    title="注意：强对流落区文件仅管理员可见，考生无法查看，用于自动评分对比"
                    type="info"
                    :closable="false"
                    show-icon
                    style="margin-bottom: 10px;"
                  />
                  <el-upload
                    ref="areaFileUpload"
                    :action="areaFileUploadUrl"
                    :headers="uploadHeaders"
                    :data="uploadData"
                    :file-list="areaFileList"
                    :on-success="handleAreaFileUploadSuccess"
                    :on-error="handleAreaFileUploadError"
                    :on-remove="handleAreaFileRemove"
                    :before-upload="beforeAreaFileUpload"
                    multiple
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将强对流落区文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      支持.json、.geojson、.shp等地理数据格式文件，单个文件不超过10MB
                    </div>
                  </el-upload>
                </div>

                <div v-if="areaFileList.length > 0" class="file-list">
                  <h5>已上传落区文件：</h5>
                  <div class="file-items">
                    <div
                      v-for="file in areaFileList"
                      :key="file.uid || file.id"
                      class="file-item"
                    >
                      <div class="file-info">
                        <i class="el-icon-map-location" />
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                        <el-tag v-if="file.areaType" :type="getAreaTypeTagType(file.areaType)" size="mini">
                          {{ getAreaTypeLabel(file.areaType) }}
                        </el-tag>
                      </div>
                      <div class="file-actions">
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-view"
                          @click="previewAreaFile(file)"
                        >
                          预览
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-download"
                          @click="downloadFile(file)"
                        >
                          下载
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          style="color: #F56C6C;"
                          icon="el-icon-delete"
                          @click="removeAreaFile(file)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 站点配置 -->
          <div class="form-section">
            <h4 class="section-title">预报站点配置</h4>

            <div class="station-config">
              <div class="config-header">
                <span>预报站点列表（共需4个站点）</span>
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-plus"
                  :disabled="formData.stations.length >= 4"
                  @click="handleAddStation"
                >
                  添加站点
                </el-button>
              </div>

              <div class="station-list">
                <!-- 站点配置表格 -->
                <el-table
                  v-if="formData.stations.length > 0"
                  :data="formData.stations"
                  border
                  style="width: 100%"
                >
                  <el-table-column prop="name" label="站点名称" width="150">
                    <template slot-scope="scope">
                      <el-form-item :prop="`stations.${scope.$index}.name`">
                        <el-input
                          v-model="scope.row.name"
                          placeholder="请输入站点名称"
                          size="small"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>

                  <el-table-column label="短时强降水" width="180">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.rainfallLevel"
                        placeholder="请选择"
                        clearable
                        size="small"
                        style="width: 100%;"
                      >
                        <el-option label="无" value="none" />
                        <el-option label="20≤R1＜40mm/h" value="level1" />
                        <el-option label="40≤R1＜80mm/h" value="level2" />
                        <el-option label="80≤R1mm/h以上" value="level3" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="雷暴大风" width="200">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.windLevel"
                        placeholder="请选择"
                        clearable
                        size="small"
                        style="width: 100%;"
                      >
                        <el-option label="无" value="none" />
                        <el-option label="8级≤Wg＜10级或6级≤W2＜8级" value="moderate" />
                        <el-option label="10级≤Wg＜12级或8级≤W2＜10级" value="severe" />
                        <el-option label="12级≤Wg或龙卷或10级≤W2" value="extreme" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="冰雹" width="150">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.hailLevel"
                        placeholder="请选择"
                        clearable
                        size="small"
                        style="width: 100%;"
                      >
                        <el-option label="无" value="none" />
                        <el-option label="2cm以上大冰雹" value="large" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="80">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="mini"
                        style="color: #F56C6C;"
                        icon="el-icon-delete"
                        @click="removeStation(scope.$index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <div v-if="formData.stations.length === 0" class="empty-stations">
                  <el-empty
                    description="暂无配置站点，请添加4个预报站点"
                    :image-size="80"
                  />
                </div>

                <div v-if="formData.stations.length > 0 && formData.stations.length < 4" class="station-count-tip">
                  <el-alert
                    :title="`当前已配置 ${formData.stations.length} 个站点，还需添加 ${4 - formData.stations.length} 个站点`"
                    type="warning"
                    :closable="false"
                    show-icon
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 标准预报依据录入 -->
          <div class="form-section">
            <h4 class="section-title">标准预报依据</h4>
            <div class="section-desc">
              <el-alert
                title="请详细录入标准预报依据，用于人工批卷时与考生答案对比"
                type="info"
                :closable="false"
                show-icon
              />
            </div>

            <el-form-item label="标准预报依据" prop="standardReasoning">
              <el-input
                v-model="formData.standardReasoning"
                type="textarea"
                :rows="8"
                placeholder="请详细录入标准预报依据，包括：&#10;1. 分级依据阐述：各类强对流天气的分级判断标准&#10;2. 极端天气预报理由：极端天气形成机制和预报要点&#10;3. 气象分析：基于MICAPS资料的专业分析&#10;4. 预报逻辑：预报思路和判断过程"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>

            <div class="reasoning-tips">
              <h6>录入要点提示：</h6>
              <ul>
                <li><strong>分级依据阐述（10分）：</strong>详细说明短时强降水、雷暴大风、冰雹的分级判断标准</li>
                <li><strong>极端天气预报理由（10分）：</strong>阐述极端天气形成机制、预报关键指标和预警发布依据</li>
                <li><strong>气象分析：</strong>基于提供的MICAPS资料进行专业分析</li>
                <li><strong>预报逻辑：</strong>说明预报思路和判断过程</li>
              </ul>
            </div>
          </div>

        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          {{ isEditMode ? '更新' : '保存' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ConvectionQuestionAPI from '@/api/convection/question'
import { getToken } from '@/utils/auth'

export default {
  name: 'ConvectionQuestionManage',

  data() {
    return {
      // 搜索表单
      searchForm: {
        title: '',
        difficulty: ''
      },

      // 表格数据
      tableData: [],
      selectedRows: [],
      loading: false,
      updating: false,

      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },

      // 对话框
      dialogVisible: false,
      dialogMode: 'create', // create, edit, view
      formLoading: false,
      saving: false,

      // 表单数据
      formData: {
        id: null,
        title: '',
        difficulty: 'medium',
        micapsFiles: [],
        areaFiles: [],
        stations: [],
        forecastRegion: {
          centerLon: 116.4,
          centerLat: 39.9,
          zoom: 8
        },
        standardReasoning: ''
      },

      // 文件上传
      micapsFileList: [],
      uploadUrl: `${process.env.VUE_APP_BASE_API}/exam/api/convection/question/upload/micaps`,
      uploadHeaders: {},
      uploadData: {},

      // 强对流落区文件上传
      areaFileList: [],
      areaFileUploadUrl: `${process.env.VUE_APP_BASE_API}/exam/api/convection/question/upload/area`,
      areaUploadHeaders: {},
      areaUploadData: {},

      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入试题标题', trigger: 'blur' },
          { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        difficulty: [
          { required: true, message: '请选择试题难度', trigger: 'change' }
        ],
        standardReasoning: [
          { required: true, message: '请输入标准预报依据', trigger: 'blur' },
          { min: 50, max: 2000, message: '标准预报依据长度在 50 到 2000 个字符', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    dialogTitle() {
      const titleMap = {
        create: '新建试题',
        edit: '编辑试题'
      }
      return titleMap[this.dialogMode] || '试题管理'
    },

    isEditMode() {
      return this.dialogMode === 'edit'
    },

    isCreateMode() {
      return this.dialogMode === 'create'
    }
  },

  created() {
    this.loadTableData()
    // 设置上传文件的Token
    this.uploadHeaders = { token: getToken() }
    this.areaUploadHeaders = { token: getToken() }
  },

  methods: {
    // 获取难度标签类型
    getDifficultyTagType(difficulty) {
      const typeMap = {
        'easy': 'success',
        'medium': 'warning',
        'hard': 'danger'
      }
      return typeMap[difficulty] || 'info'
    },

    // 获取难度标签文本
    getDifficultyLabel(difficulty) {
      const labelMap = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      }
      return labelMap[difficulty] || '未知'
    },

    // 获取天气类型标签类型
    getWeatherTypeTagType(type) {
      const typeMap = {
        'heavy-rainfall': 'primary',
        'thunderstorm-wind': 'warning',
        'hail': 'danger',
        'tornado': 'info'
      }
      return typeMap[type] || 'default'
    },

    // 获取天气类型标签文本
    getWeatherTypeLabel(type) {
      const labelMap = {
        'heavy-rainfall': '短时强降水',
        'thunderstorm-wind': '雷暴大风',
        'hail': '冰雹',
        'tornado': '龙卷'
      }
      return labelMap[type] || '未知类型'
    },

    // 获取落区文件类型标签类型
    getAreaTypeTagType(type) {
      const typeMap = {
        'json': 'info',
        'geojson': 'success',
        'shp': 'warning'
      }
      return typeMap[type] || 'default'
    },

    // 获取落区文件类型标签文本
    getAreaTypeLabel(type) {
      const labelMap = {
        'json': 'JSON',
        'geojson': 'GeoJSON',
        'shp': 'Shapefile'
      }
      return labelMap[type] || '未知类型'
    },

    // 获取短时强降水等级标签文本
    getRainfallLevelLabel(level) {
      const labelMap = {
        'level1': '20≤R1＜40mm/h',
        'level2': '40≤R1＜80mm/h',
        'level3': '80≤R1mm/h以上'
      }
      return labelMap[level] || '未知'
    },

    // 获取雷暴大风等级标签文本
    getWindLevelLabel(level) {
      const labelMap = {
        'moderate': '8级≤Wg＜10级或6级≤W2＜8级',
        'severe': '10级≤Wg＜12级或8级≤W2＜10级',
        'extreme': '12级≤Wg或龙卷或10级≤W2'
      }
      return labelMap[level] || '未知'
    },

    // 获取冰雹等级标签文本
    getHailLevelLabel(level) {
      const labelMap = {
        'large': '2cm以上大冰雹'
      }
      return labelMap[level] || '未知'
    },

    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          size: this.pagination.pageSize,
          title: this.searchForm.title,
          difficulty: this.searchForm.difficulty
        }

        const response = await ConvectionQuestionAPI.getQuestionList(params)

        this.tableData = response.data.records || response.data.list || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        console.error('加载试题列表失败:', error)
        this.$message.error('加载试题列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        difficulty: ''
      }
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadTableData()
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadTableData()
    },

    // 选择变化
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },

    // 新建试题
    handleCreate() {
      this.dialogMode = 'create'
      this.resetFormData()
      this.dialogVisible = true
    },

    // 编辑试题
    async handleEdit(row) {
      this.dialogMode = 'edit'
      await this.loadQuestionDetail(row.id)
      this.dialogVisible = true
    },

    // 删除试题
    handleDelete(row) {
      this.$confirm('确定要删除这个试题吗？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await ConvectionQuestionAPI.deleteQuestion(row.id)
          this.$message.success('试题删除成功')
          this.loadTableData()
        } catch (error) {
          this.$message.error('试题删除失败')
          console.error(error)
        }
      })
    },

    // 批量操作
    handleBatchEnable() {
      this.batchUpdateStatus(1, '启用')
    },

    handleBatchDisable() {
      this.batchUpdateStatus(0, '禁用')
    },

    async batchUpdateStatus(status, action) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要操作的试题')
        return
      }

      this.$confirm(`确定要批量${action}选中的试题吗？`, `确认批量${action}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async() => {
        try {
          const promises = this.selectedRows.map(row =>
            ConvectionQuestionAPI.updateQuestion(row.id, { status })
          )

          await Promise.all(promises)
          this.$message.success(`批量${action}成功`)
          this.loadTableData()
        } catch (error) {
          this.$message.error(`批量${action}失败`)
          console.error(error)
        }
      })
    },

    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的试题')
        return
      }

      this.$confirm('确定要批量删除选中的试题吗？删除后无法恢复。', '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const promises = this.selectedRows.map(row =>
            ConvectionQuestionAPI.deleteQuestion(row.id)
          )

          await Promise.all(promises)
          this.$message.success('批量删除成功')
          this.loadTableData()
        } catch (error) {
          this.$message.error('批量删除失败')
          console.error(error)
        }
      })
    },

    // 加载试题详情
    async loadQuestionDetail(questionId) {
      this.formLoading = true
      try {
        const response = await ConvectionQuestionAPI.getQuestionDetail(questionId)
        const data = response.data

        this.formData = {
          id: data.id,
          title: data.title || '',
          description: data.description || '',
          difficulty: data.difficulty || 'medium',
          micapsFiles: data.micapsFiles || [],
          areaFiles: data.areaFiles || [],
          stations: data.stations || [],
          forecastRegion: data.forecastRegion || {
            centerLon: 116.4,
            centerLat: 39.9,
            zoom: 8
          },
          standardReasoning: data.standardReasoning || ''
        }

        // 设置文件列表
        this.micapsFileList = (data.micapsFiles || []).map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          url: file.url,
          uid: file.id
        }))

        // 设置落区文件列表
        this.areaFileList = (data.areaFiles || []).map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          url: file.url,
          uid: file.id,
          areaType: file.areaType
        }))
      } catch (error) {
        this.$message.error('加载试题详情失败')
        console.error(error)
      } finally {
        this.formLoading = false
      }
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        id: null,
        title: '',
        difficulty: 'medium',
        micapsFiles: [],
        areaFiles: [],
        stations: [],
        forecastRegion: {
          centerLon: 116.4,
          centerLat: 39.9,
          zoom: 8
        },
        standardReasoning: ''
      }
      this.micapsFileList = []
      this.areaFileList = []

      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.questionForm) {
          this.$refs.questionForm.clearValidate()
        }
      })
    },

    // 添加站点
    handleAddStation() {
      this.formData.stations.push({
        id: null,
        name: '',
        rainfallLevel: null,
        windLevel: null,
        hailLevel: null
      })
    },

    // 删除站点
    removeStation(index) {
      this.formData.stations.splice(index, 1)
    },

    // 文件上传相关
    beforeMicapsUpload(file) {
      const isValidType = /\.(000|024|dat|txt)$/i.test(file.name)
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isValidType) {
        this.$message.error('只支持 .000、.024、.dat、.txt 格式的文件')
        return false
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB')
        return false
      }
      return true
    },

    handleMicapsUploadSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.micapsFileList = fileList
        this.formData.micapsFiles = fileList.map(f => ({
          id: f.response?.data?.id || f.id,
          name: f.name,
          size: f.size,
          url: f.response?.data?.url || f.url
        }))
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    handleMicapsUploadError(error, file, fileList) {
      this.$message.error('文件上传失败')
      console.error(error)
    },

    handleMicapsFileRemove(file, fileList) {
      this.micapsFileList = fileList
      this.formData.micapsFiles = fileList.map(f => ({
        id: f.response?.data?.id || f.id,
        name: f.name,
        size: f.size,
        url: f.response?.data?.url || f.url
      }))
    },

    // 强对流落区文件上传相关
    beforeAreaFileUpload(file) {
      const isValidType = /\.(json|geojson|shp)$/i.test(file.name)
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只支持 .json、.geojson、.shp 格式的文件')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB')
        return false
      }
      return true
    },

    handleAreaFileUploadSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.areaFileList = fileList
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    handleAreaFileUploadError(error, file, fileList) {
      this.$message.error('文件上传失败')
      console.error(error)
    },

    handleAreaFileRemove(file, fileList) {
      this.areaFileList = fileList
    },

    // 预览落区文件
    previewAreaFile(file) {
      if (file.url) {
        window.open(file.url, '_blank')
      } else {
        this.$message.warning('文件预览链接不存在')
      }
    },

    // 下载文件
    downloadFile(file) {
      if (file.url) {
        window.open(file.url, '_blank')
      } else {
        this.$message.warning('文件下载链接不存在')
      }
    },

    // 删除文件
    removeFile(file) {
      const index = this.micapsFileList.findIndex(f =>
        (f.uid || f.id) === (file.uid || file.id)
      )
      if (index !== -1) {
        this.micapsFileList.splice(index, 1)
        this.formData.micapsFiles.splice(index, 1)
      }
    },

    // 删除落区文件
    removeAreaFile(file) {
      const index = this.areaFileList.findIndex(f =>
        (f.uid || f.id) === (file.uid || file.id)
      )
      if (index !== -1) {
        this.areaFileList.splice(index, 1)
      }
    },

    // 保存试题
    async handleSave() {
      try {
        await this.$refs.questionForm.validate()

        this.saving = true

        const data = {
          ...this.formData,
          micapsFiles: this.formData.micapsFiles,
          areaFiles: this.areaFileList.map(f => ({
            id: f.response?.data?.id || f.id,
            name: f.name,
            size: f.size,
            url: f.response?.data?.url || f.url,
            areaType: f.areaType || this.getFileTypeFromName(f.name)
          })),
          stations: this.formData.stations.filter(station =>
            station.name && station.name.trim() !== ''
          )
        }

        if (this.isEditMode) {
          await ConvectionQuestionAPI.updateQuestion(data.id, data)
          this.$message.success('试题更新成功')
        } else {
          await ConvectionQuestionAPI.createQuestion(data)
          this.$message.success('试题创建成功')
        }

        this.dialogVisible = false
        this.loadTableData()
      } catch (error) {
        if (error !== false) { // 排除表单验证失败
          this.$message.error('保存试题失败')
          console.error(error)
        }
      } finally {
        this.saving = false
      }
    },

    // 关闭对话框
    handleDialogClose() {
      if (this.saving) {
        return
      }

      this.dialogVisible = false
      this.resetFormData()
    },

    // 工具方法
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 根据文件名获取文件类型
    getFileTypeFromName(fileName) {
      if (!fileName) return 'unknown'
      const ext = fileName.split('.').pop().toLowerCase()
      const typeMap = {
        'json': 'json',
        'geojson': 'geojson',
        'shp': 'shp'
      }
      return typeMap[ext] || 'unknown'
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-question-manage {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 25px 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;

    .header-left {
      h2 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 26px;
        font-weight: 700;
        background: linear-gradient(135deg, #409eff, #67c23a);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .header-desc {
        color: #606266;
        font-size: 15px;
        line-height: 1.5;
        max-width: 600px;
      }
    }

    .header-right {
      .el-button {
        border-radius: 8px;
        font-weight: 500;
        padding: 12px 20px;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .search-section {
    margin-bottom: 25px;
    padding: 25px 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
          font-size: 14px;
        }

        .el-input__inner,
        .el-select .el-input__inner {
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }

        .el-button {
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }
  }

  .table-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;

    ::v-deep .el-table {
      .el-table__header {
        background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);

        th {
          background: transparent;
          color: #606266;
          font-weight: 600;
          font-size: 14px;
          border-bottom: 2px solid #e9ecef;
        }
      }

      .el-table__body {
        tr {
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
          }
        }

        td {
          border-bottom: 1px solid #f0f0f0;
          padding: 12px 0;
        }
      }
    }

    .question-title {
      .title-text {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .title-meta {
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
      }
    }

    .question-desc {
      color: #606266;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 30px;
      background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
      border-top: 1px solid #e9ecef;

      .batch-info {
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }

      .batch-buttons {
        display: flex;
        gap: 12px;

        .el-button {
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .pagination-section {
      padding: 25px 30px;
      text-align: center;
      border-top: 1px solid #e9ecef;
      background: #fafbfc;

      ::v-deep .el-pagination {
        .el-pagination__total,
        .el-pagination__jump {
          color: #606266;
          font-weight: 500;
        }

        .btn-prev,
        .btn-next {
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            background: #409eff;
            color: white;
          }
        }

        .el-pager li {
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            background: #409eff;
            color: white;
          }

          &.active {
            background: #409eff;
            color: white;
          }
        }
      }
    }
  }

  .question-form {
    .form-section {
      margin-bottom: 35px;
      padding: 25px;
      background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
      border-radius: 12px;
      border: 1px solid #e9ecef;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #409eff, #67c23a, #e6a23c);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        margin: 0 0 25px 0;
        padding-bottom: 15px;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
        border-bottom: 2px solid #e1f0ff;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 60px;
          height: 2px;
          background: linear-gradient(90deg, #409eff, #67c23a);
        }
      }

      .section-desc {
        margin-bottom: 20px;
      }
    }

    .file-upload-area {
      margin-bottom: 20px;

      .upload-title {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background: linear-gradient(135deg, #409eff, #67c23a);
          border-radius: 2px;
        }
      }

      ::v-deep .el-upload-dragger {
        width: 100%;
        height: 110px;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          border-color: #409eff;
          background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);

          &::before {
            opacity: 1;
          }

          .el-icon-upload {
            color: #409eff;
            transform: scale(1.1);
          }

          .el-upload__text em {
            color: #409eff;
          }
        }

        .el-icon-upload {
          font-size: 28px;
          color: #c0c4cc;
          margin-bottom: 10px;
          transition: all 0.3s ease;
        }

        .el-upload__text {
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
          text-align: center;
          font-weight: 500;

          em {
            color: #409eff;
            font-style: normal;
            font-weight: 600;
            transition: color 0.3s ease;
          }
        }

        .el-upload__tip {
          margin-top: 8px;
          color: #909399;
          font-size: 12px;
          line-height: 1.4;
          text-align: center;
          max-width: 90%;
        }
      }
    }

    .file-list {
      h5 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          width: 3px;
          height: 14px;
          background: #67c23a;
          border-radius: 2px;
        }
      }

      .file-items {
        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: linear-gradient(135deg, #f8f9fa 0%, #f5f7fa 100%);
          border: 1px solid #e9ecef;
          border-radius: 6px;
          margin-bottom: 8px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:last-child {
            margin-bottom: 0;
          }

          &:hover {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-color: #409eff;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
          }

          .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;

            i {
              color: #67c23a;
              font-size: 16px;
              transition: transform 0.3s ease;
            }

            .file-name {
              color: #303133;
              font-weight: 500;
              font-size: 13px;
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .file-size {
              color: #909399;
              font-size: 12px;
              background: #f0f0f0;
              padding: 2px 6px;
              border-radius: 3px;
            }
          }

          .file-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;
              border-radius: 4px;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }
            }
          }
        }
      }
    }

    ::v-deep .el-form-item {
      margin-bottom: 22px;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
        font-size: 14px;
      }

      .el-form-item__content {
        .el-input__inner,
        .el-textarea__inner {
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }

        .el-select {
          .el-input__inner {
            border-radius: 6px;
          }
        }

        .el-input-number {
          .el-input__inner {
            border-radius: 6px;
          }
        }
      }
    }

    .station-config {
      .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
        border-radius: 8px;
        border: 1px solid #e9ecef;

        span {
          color: #303133;
          font-weight: 600;
          font-size: 15px;
        }

        .el-button {
          border-radius: 6px;
          font-weight: 500;
        }
      }

      .station-list {
        .empty-stations {
          text-align: center;
          padding: 50px 20px;
          background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
          border-radius: 8px;
          margin-bottom: 20px;
          border: 2px dashed #d9d9d9;
          color: #909399;
        }

        .station-count-tip {
          margin-top: 15px;
          padding: 10px 15px;
          background: #f0f9ff;
          border: 1px solid #b3d8ff;
          border-radius: 6px;
          color: #409eff;
          font-size: 13px;
        }

        // 表格内表单项样式优化
        ::v-deep .el-table {
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          .el-table__header {
            background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
          }

          .el-form-item {
            margin-bottom: 0;

            .el-form-item__content {
              margin-left: 0 !important;
            }
          }

          .el-table__body-wrapper {
            .el-table__row {
              transition: all 0.3s ease;

              &:hover {
                background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
              }
            }
          }
        }
      }
    }

    .reasoning-tips {
      margin-top: 25px;
      padding: 20px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
      border-radius: 8px;
      border: 1px solid #b3d8ff;

      h6 {
        margin-bottom: 15px;
        color: #303133;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      ul {
        padding-left: 20px;
        list-style: none;

        li {
          color: #606266;
          font-size: 14px;
          line-height: 1.8;
          margin-bottom: 8px;
          position: relative;
          padding-left: 20px;

          &::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #409eff;
            font-weight: bold;
            font-size: 16px;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // 天气类型显示样式
  .weather-types-display {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #409eff;

    .types-label {
      font-size: 12px;
      color: #606266;
      margin-right: 8px;
    }
  }

  // 操作按钮样式
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;

    .el-button {
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
    .convection-question-manage {
      .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
      }

      .search-section .search-form {
        .el-form-item {
          margin-bottom: 15px;

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }

      .question-form {
        .file-upload-area {
          margin-bottom: 15px;
        }

        .file-list {
          .file-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .file-actions {
              width: 100%;
              justify-content: flex-end;
            }
          }
        }

        .station-config .station-list .station-item {
          flex-direction: column;

          .station-actions {
            padding-top: 0;
            width: 100%;
            text-align: center;
          }
        }
      }
    }
  }
</style>
