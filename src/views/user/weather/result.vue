<template>
  <div class="weather-exam-result">
    <!-- 考试结果头部信息 -->
    <div class="result-header">
      <div class="header-content">
        <div class="result-title">
          <h2>{{ examData.title || '历史个例考试结果' }}</h2>
          <div class="exam-meta">
            <div class="meta-item">
              <i class="el-icon-time" />
              <span>考试时长：{{ examData.duration || examData.totalTime || 60 }}分钟</span>
            </div>
            <div class="meta-item">
              <i class="el-icon-star-on" />
              <span>总分：{{ examData.totalScore || 100 }}分</span>
            </div>
            <div class="meta-item score-display">
              <i class="el-icon-trophy" />
              <span>您的得分：</span>
              <span class="final-score">{{ resultData.totalScore || 0 }}</span>
              <span>分</span>
            </div>
          </div>
        </div>

        <!-- 得分统计 -->

      </div>
    </div>

    <!-- 考试内容结果 -->
    <div class="result-content">
      <!-- 第一部分：降水分级落区预报结果 -->
      <el-card class="result-section" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>第一部分：降水分级落区预报</h3>
            <div class="section-score">
              <el-tag type="primary" size="medium">
                得分：{{ resultData.precipitationScore || 0 }}/40分
              </el-tag>
            </div>
          </div>
        </div>

        <div class="precipitation-result-section">
          <!-- 题目信息 -->
          <div class="question-info">
            <div class="question-content">
              <h4>题目要求：</h4>
              <p>{{ questionData.precipitationContent || '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。' }}</p>
            </div>
            <div class="region-info">
              <h4>预报区域：</h4>
              <el-tag type="success" size="medium">{{ getRegionLabel(questionData.precipitationRegion || 'region1') }}</el-tag>
            </div>
          </div>

          <!-- 降水落区结果展示 -->
          <div class="precipitation-result-display">
            <div class="result-tabs">
              <el-tabs v-model="precipitationActiveTab" class="result-tabs-container">
                <el-tab-pane label="您的答案" name="user">
                  <div class="precipitation-display-container">
                    <precipitation-drawing
                      ref="userPrecipitationDrawing"
                      :initial-data="userPrecipitationData"
                      :region="questionData.precipitationRegion || 'region1'"
                      :readonly="true"
                      :show-legend="true"
                    />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="标准答案" name="standard">
                  <div class="precipitation-display-container">
                    <precipitation-drawing
                      ref="standardPrecipitationDrawing"
                      :initial-data="standardPrecipitationData"
                      :region="questionData.precipitationRegion || 'region1'"
                      :readonly="true"
                      :show-legend="true"
                    />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="对比分析" name="comparison">
                  <div class="comparison-analysis">
                    <div class="analysis-summary">
                      <h4>评分分析</h4>
                      <div class="analysis-items">
                        <div class="analysis-item">
                          <span class="analysis-label">绘制完整性：</span>
                          <span class="analysis-value">{{ precipitationAnalysis.completeness || '良好' }}</span>
                        </div>
                        <div class="analysis-item">
                          <span class="analysis-label">区域准确性：</span>
                          <span class="analysis-value">{{ precipitationAnalysis.accuracy || '较好' }}</span>
                        </div>
                        <div class="analysis-item">
                          <span class="analysis-label">等级划分：</span>
                          <span class="analysis-value">{{ precipitationAnalysis.grading || '基本正确' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 第二部分：灾害性天气预报结果 -->
      <el-card class="result-section" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>第二部分：灾害性天气预报</h3>
            <div class="section-score">
              <el-tag type="success" size="medium">
                得分：{{ resultData.weatherScore || 0 }}/60分
              </el-tag>
            </div>
          </div>
        </div>

        <div class="weather-result-section">
          <!-- 天气预报结果表格 -->
          <div class="result-table-container">
            <weather-result-table
              ref="weatherResultTable"
              :question="questionData"
              :user-answers="userWeatherAnswers"
              :standard-answers="standardWeatherAnswers"
              :scoring-result="weatherScoringResult"
              :readonly="true"
            />
          </div>
        </div>
      </el-card>

      <!-- 详细评分信息 -->
      <el-card class="result-section scoring-details" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>详细评分信息</h3>
          </div>
        </div>

        <div class="scoring-breakdown">
          <!-- 第一部分评分详情 -->
          <div class="scoring-part">
            <h4>第一部分 - 降水分级落区预报</h4>

            <!-- 降水落区详细评分信息 -->
            <div v-if="precipitationAreaDetails" class="precipitation-area-details">
              <div class="detail-section">
                <h5>降水落区评分详情</h5>
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="detail-label">总得分：</span>
                    <span class="detail-value score-highlight">{{ formatScoreToTwoDecimals(precipitationAreaDetails.score) }}分</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">总站点数：</span>
                    <span class="detail-value">{{ precipitationAreaDetails.totalStations || 0 }}个</span>
                  </div>
                </div>
              </div>



              <!-- 各量级TS评分对比详情 -->
              <div class="integrated-level-scoring">
                <h6>各量级TS评分对比详情</h6>
                <div class="level-scoring-tabs">
                  <el-tabs v-model="integratedLevelActiveTab" type="card" class="integrated-level-tabs-container">
                    <!-- 晴雨量级 -->
                    <el-tab-pane label="晴雨" name="晴雨">
                      <div class="integrated-level-content">
                        <!-- 晴雨量级TS评分对比卡片 -->
                        <div class="ts-comparison-cards">
                          <!-- 我的晴雨TS评分 -->
                          <div class="ts-score-card student-card">
                            <div class="card-header">
                              <div class="card-title">我的晴雨TS评分</div>
                            </div>
                            <div class="card-content">
                              <div class="main-score">
                                <span class="score-label">TS评分：</span>
                                <span class="score-value">{{ formatScore(precipitationAreaDetails.studentLevelTSDetails && precipitationAreaDetails.studentLevelTSDetails['晴雨'] ? precipitationAreaDetails.studentLevelTSDetails['晴雨'].tsScore : 0) }}</span>
                              </div>
                              <div v-if="precipitationAreaDetails.studentLevelTSDetails && precipitationAreaDetails.studentLevelTSDetails['晴雨']" class="student-details">
                                <div class="detail-item">
                                  <span class="detail-label">正确预报有雨：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails['晴雨'].correctA }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">正确预报无雨：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails['晴雨'].correctD || 0 }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">空报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails['晴雨'].wrongB }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">漏报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails['晴雨'].missedC }}个</span>
                                </div>
                                <div class="formula-item">
                                  <span class="formula-label">计算公式：</span>
                                  <span class="formula-value">{{ precipitationAreaDetails.studentLevelTSDetails['晴雨'].formula }}</span>
                                </div>
                              </div>
                              <div class="total-score">
                                <span class="score-label">总得分：</span>
                                <span class="score-value highlight">{{ formatScore(precipitationAreaDetails.skillScores ? precipitationAreaDetails.skillScores['晴雨'] : 0) }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- MESO晴雨TS评分 -->
                          <div class="ts-score-card meso-card">
                            <div class="card-header">
                              <div class="card-title">MESO晴雨TS评分</div>
                            </div>
                            <div class="card-content">
                              <div class="main-score">
                                <span class="score-label">TS评分：</span>
                                <span class="score-value">{{ formatScore(precipitationAreaDetails.mesoLevelTSDetails && precipitationAreaDetails.mesoLevelTSDetails['晴雨'] ? precipitationAreaDetails.mesoLevelTSDetails['晴雨'].tsScore : 0) }}</span>
                              </div>
                              <div v-if="precipitationAreaDetails.mesoLevelTSDetails && precipitationAreaDetails.mesoLevelTSDetails['晴雨']" class="meso-details">
                                <div class="detail-item">
                                  <span class="detail-label">正确预报有雨：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails['晴雨'].correctA }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">正确预报无雨：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails['晴雨'].correctD || 0 }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">空报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails['晴雨'].wrongB }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">漏报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails['晴雨'].missedC }}个</span>
                                </div>
                                <div class="formula-item">
                                  <span class="formula-label">计算公式：</span>
                                  <span class="formula-value">{{ precipitationAreaDetails.mesoLevelTSDetails['晴雨'].formula }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>

                    <!-- 其他量级 -->
                    <el-tab-pane
                      v-for="level in getOtherLevels()"
                      :key="level"
                      :label="level"
                      :name="level"
                    >
                      <div class="integrated-level-content">
                        <!-- 该量级TS评分对比卡片 -->
                        <div class="ts-comparison-cards">
                          <!-- 我的该量级TS评分 -->
                          <div class="ts-score-card student-card">
                            <div class="card-header">
                              <div class="card-title">我的{{ level }}TS评分</div>
                            </div>
                            <div class="card-content">

                              <div class="main-score">
                                <span class="score-label">TS评分：</span>
                                <span class="score-value">{{ formatScore(precipitationAreaDetails.studentLevelTSDetails && precipitationAreaDetails.studentLevelTSDetails[level] ? precipitationAreaDetails.studentLevelTSDetails[level].tsScore : 0) }}</span>
                              </div>
                              <div v-if="precipitationAreaDetails.studentLevelTSDetails && precipitationAreaDetails.studentLevelTSDetails[level]" class="student-details">
                                <div class="detail-item">
                                  <span class="detail-label">正确预报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails[level].correctA }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">错误预报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails[level].wrongB }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">漏报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.studentLevelTSDetails[level].missedC }}个</span>
                                </div>
                                <div class="formula-item">
                                  <span class="formula-label">计算公式：</span>
                                  <span class="formula-value">{{ precipitationAreaDetails.studentLevelTSDetails[level].formula }}</span>
                                </div>
                              </div>
                              <div class="total-score">
                                <span class="score-label">总得分：</span>
                                <span class="score-value highlight">{{ formatScore(precipitationAreaDetails.skillScores ? precipitationAreaDetails.skillScores[level] : 0) }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- MESO该量级TS评分 -->
                          <div class="ts-score-card meso-card">
                            <div class="card-header">
                              <div class="card-title">MESO {{ level }} TS评分</div>
                            </div>
                            <div class="card-content">
                              <div class="main-score">
                                <span class="score-label">TS评分：</span>
                                <span class="score-value">{{ formatScore(precipitationAreaDetails.mesoLevelTSDetails && precipitationAreaDetails.mesoLevelTSDetails[level] ? precipitationAreaDetails.mesoLevelTSDetails[level].tsScore : 0) }}</span>
                              </div>
                              <div v-if="precipitationAreaDetails.mesoLevelTSDetails && precipitationAreaDetails.mesoLevelTSDetails[level]" class="meso-details">
                                <div class="detail-item">
                                  <span class="detail-label">正确预报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails[level].correctA }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">错误预报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails[level].wrongB }}个</span>
                                </div>
                                <div class="detail-item">
                                  <span class="detail-label">漏报：</span>
                                  <span class="detail-value">{{ precipitationAreaDetails.mesoLevelTSDetails[level].missedC }}个</span>
                                </div>
                                <div class="formula-item">
                                  <span class="formula-label">计算公式：</span>
                                  <span class="formula-value">{{ precipitationAreaDetails.mesoLevelTSDetails[level].formula }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>



            </div>

            <!-- 站点详情按钮 -->
            <div class="station-details-section">
              <el-button
                type="primary"
                size="medium"
                icon="el-icon-view"
                :loading="loadingStationDetails"
                @click="showStationDetails"
              >
                查看站点得分详情
              </el-button>
            </div>

          </div>

          <!-- 传统评分项目（作为备用显示） -->
          <div v-if="!precipitationAreaDetails" class="scoring-items">
            <div class="scoring-item">
              <span class="item-name">降水区域绘制</span>
              <span class="item-score">{{ precipitationScoring.areaScore || 0 }}/20分</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">等级划分准确性</span>
              <span class="item-score">{{ precipitationScoring.levelScore || 0 }}/15分</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">区域完整性</span>
              <span class="item-score">{{ precipitationScoring.completenessScore || 0 }}/5分</span>
            </div>
          </div>
        </div>

        <!-- 第二部分评分详情 -->
        <div class="scoring-part">
          <h4>第二部分 - 灾害性天气预报（单站总分10分）</h4>

          <!-- 评分要素说明 -->
          <div class="scoring-elements-info">
            <div class="elements-grid">
              <div class="element-info">
                <span class="element-name">08-08最大风力(级)</span>
                <span class="element-score">1.0分</span>
              </div>
              <div class="element-info">
                <span class="element-name">08-08最大风力时的风向</span>
                <span class="element-score">1.0分</span>
              </div>
              <div class="element-info">
                <span class="element-name">最低气温℃</span>
                <span class="element-score">2.0分</span>
              </div>
              <div class="element-info">
                <span class="element-name">最高气温℃</span>
                <span class="element-score">2.0分</span>
              </div>
              <div class="element-info">
                <span class="element-name">08-08降水(雨、雪)量级</span>
                <span class="element-score">2.0分</span>
              </div>
              <div class="element-info">
                <span class="element-name">灾害性天气类型</span>
                <span class="element-score">2.0分</span>
              </div>
            </div>
          </div>

          <!-- 实际得分情况 -->
          <div class="scoring-items">
            <div class="scoring-item">
              <span class="item-name">风力预报 (满分1.0分)</span>
              <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.windForceScore || 0) }}分</span>
              <span class="item-detail">{{ getWindForceScoreDetail() }}</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">风向预报 (满分1.0分)</span>
              <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.windDirectionScore || 0) }}分</span>
              <span class="item-detail">{{ getWindDirectionScoreDetail() }}</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">最低气温 (满分2.0分)</span>
              <span class="item-score">{{ formatScoreToTwoDecimals((weatherScoring.minTemperatureScore || 0)) }}分</span>
              <span class="item-detail">容差±2℃</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">最高气温 (满分2.0分)</span>
              <span class="item-score">{{ formatScoreToTwoDecimals((weatherScoring.maxTemperatureScore || 0)) }}分</span>
              <span class="item-detail">容差±2℃</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">降水预报 (满分2.0分)</span>
              <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.precipitationScore || 0) }}分</span>
              <span class="item-detail">{{ getPrecipitationScoreDetail() }}</span>
            </div>
            <div class="scoring-item">
              <span class="item-name">灾害天气预报 (满分2.0分)</span>
              <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.disasterScore || 0) }}分</span>
              <span class="item-detail">{{ getDisasterWeatherScoreDetail() }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="result-actions">
        <el-button size="large" @click="goBack">
          <i class="el-icon-back" />
          返回考试列表
        </el-button>
      </div>
    </div>

    <!-- 站点详情弹窗 -->
    <el-dialog
    title="站点得分详情"
    :visible.sync="stationDetailsVisible"
    width="90%"
    :close-on-click-modal="false"
    class="station-details-dialog"
  >
    <div v-loading="loadingStationDetails" class="station-details-content">
      <div v-if="stationDetailsList && stationDetailsList.length > 0">
        <!-- 统计摘要 -->
        <div class="station-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">总站点数</div>
                <div class="summary-value">{{ stationDetailsList.length }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">有降水站点</div>
                <div class="summary-value">{{ getRainStationCount() }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">预报正确站点</div>
                <div class="summary-value">{{ getCorrectStationCount() }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">正确率</div>
                <div class="summary-value">{{ getStationAccuracy() }}%</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 筛选条件 -->
        <div class="station-filters">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-select v-model="stationFilter.actualLevel" placeholder="实况等级" clearable @change="filterStations">
                <el-option label="全部" value="" />
                <el-option label="无雨" value="无雨" />
                <el-option label="小雨" value="小雨" />
                <el-option label="中雨" value="中雨" />
                <el-option label="大雨" value="大雨" />
                <el-option label="暴雨" value="暴雨" />
                <el-option label="大暴雨" value="大暴雨" />
                <el-option label="微量降水" value="微量降水" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="stationFilter.resultType" placeholder="预报结果" clearable @change="filterStations">
                <el-option label="全部" value="" />
                <el-option label="正确A" value="正确A" />
                <el-option label="空报B" value="空报B" />
                <el-option label="漏报C" value="漏报C" />
                <el-option label="正确D" value="正确D" />
                <el-option label="错误B" value="错误B" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input v-model="stationFilter.stationId" placeholder="站点ID" clearable @input="filterStations" />
            </el-col>
          </el-row>
        </div>

        <!-- 站点详情表格 -->
        <el-table
          :data="filteredStationList"
          stripe
          border
          height="400"
          class="station-details-table"
        >
          <el-table-column prop="stationId" label="站点ID" width="80" />
          <el-table-column label="位置" width="120">
            <template slot-scope="scope">
              {{ scope.row.longitude.toFixed(2) }}°E<br>
              {{ scope.row.latitude.toFixed(2) }}°N
            </template>
          </el-table-column>
          <el-table-column label="实况" width="100">
            <template slot-scope="scope">
              <div class="precipitation-info">
                <div class="precipitation-value">{{ scope.row.actualPrecipitation.toFixed(1) }}mm</div>
                <el-tag :type="getLevelTagType(scope.row.actualLevel)" size="mini">
                  {{ scope.row.actualLevel }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="考生预报" width="100">
            <template slot-scope="scope">
              <div class="precipitation-info">
                <div class="precipitation-value">{{ scope.row.studentForecastPrecipitation.toFixed(1) }}mm</div>
                <el-tag :type="getLevelTagType(scope.row.studentForecastLevel)" size="mini">
                  {{ scope.row.studentForecastLevel }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="MESO预报" width="100">
            <template slot-scope="scope">
              <div class="precipitation-info">
                <div class="precipitation-value">{{ scope.row.cmaMesoForecastPrecipitation.toFixed(1) }}mm</div>
                <el-tag :type="getLevelTagType(scope.row.cmaMesoForecastLevel)" size="mini">
                  {{ scope.row.cmaMesoForecastLevel }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="晴雨评分" width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.rainNoRainDetail">
                <el-tag :type="getResultTagType(scope.row.rainNoRainDetail.studentResultType)" size="mini">
                  {{ scope.row.rainNoRainDetail.studentResultType }}
                </el-tag>
                <div class="contribution-text">{{ scope.row.rainNoRainDetail.studentContribution }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="量级评分" width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.levelDetail">
                <el-tag :type="getResultTagType(scope.row.levelDetail.studentResultType)" size="mini">
                  {{ scope.row.levelDetail.studentResultType }}
                </el-tag>
                <div class="contribution-text">{{ scope.row.levelDetail.studentContribution }}</div>
                <div v-if="scope.row.levelDetail.specialRuleNote" class="special-rule-note">
                  {{ scope.row.levelDetail.specialRuleNote }}
                </div>
              </div>
              <div v-else class="no-level-scoring">
                不参与量级评分
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="no-station-data">
        <el-empty description="暂无站点详情数据" />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" icon="el-icon-download" @click="exportStationDetails">导出CSV</el-button>
      <el-button @click="stationDetailsVisible = false">关闭</el-button>
    </div>
  </el-dialog>
  </div>
</template>

<script>
import WeatherResultTable from '@/components/weather/WeatherResultTable'
import PrecipitationDrawing from '@/components/weather/PrecipitationDrawing'
import {
  getWeatherExamResult,
  getWeatherExamDetail
} from '@/api/weather/weather'

export default {
  name: 'WeatherExamResult',
  components: {
    WeatherResultTable,
    PrecipitationDrawing
  },
  data() {
    return {
      examId: '',
      examData: {},
      questionData: {},
      resultData: {},

      // 降水预报相关数据
      userPrecipitationData: null,
      standardPrecipitationData: null,
      precipitationActiveTab: 'user',
      precipitationAnalysis: {},
      precipitationScoring: {},

      // 天气预报相关数据
      userWeatherAnswers: {},
      standardWeatherAnswers: {},
      weatherScoringResult: {},
      weatherScoring: {},

      // 题目场景数据
      scenarioData: {},

      // 降水落区详细评分数据
      precipitationAreaDetails: null,

      // MESO详细记录相关数据
      mesoLevelActiveTab: '小雨',

      // 考生详细记录相关数据
      studentLevelActiveTab: '小雨',

      // 集成量级评分相关数据
      integratedLevelActiveTab: '晴雨',

      // 站点详情弹窗相关数据
      stationDetailsVisible: false,
      loadingStationDetails: false,
      stationDetailsList: [],
      filteredStationList: [],
      stationFilter: {
        actualLevel: '',
        resultType: '',
        stationId: ''
      },

      loading: false
    }
  },
  computed: {
    // 这里可以添加其他computed属性
  },
  created() {
    this.examId = this.$route.params.id
    this.initResultPage()
  },
  methods: {
    // 计算正确率
    calculateAccuracy() {
      const totalScore = this.examData.totalScore || 100
      const userScore = this.resultData.totalScore || 0
      return Math.round((userScore / totalScore) * 100)
    },

    // 初始化结果页面
    async initResultPage() {
      this.loading = true
      try {
        // 获取考试结果数据
        await this.loadExamResult()

        // 获取题目详情
        await this.loadQuestionData()
      } catch (error) {
        console.error('加载考试结果失败:', error)
        this.$message.error('加载考试结果失败')
      } finally {
        this.loading = false
      }
    },

    // 加载考试结果
    async loadExamResult() {
      try {
        const response = await getWeatherExamResult({ examId: this.examId })

        if (response.code === 0 && response.data) {
          const resultData = response.data

          // 调试：打印完整的返回数据结构
          console.log('完整的考试结果数据:', resultData)
          console.log('gradingDetails:', resultData.gradingDetails)
          if (resultData.gradingDetails) {
            console.log('gradingDetails keys:', Object.keys(resultData.gradingDetails))
            if (resultData.gradingDetails.detailResults) {
              console.log('detailResults:', resultData.gradingDetails.detailResults)
              console.log('detailResults keys:', Object.keys(resultData.gradingDetails.detailResults))
              if (resultData.gradingDetails.detailResults.precipitationArea) {
                console.log('precipitationArea:', resultData.gradingDetails.detailResults.precipitationArea)
              }
            }
          }

          // 解析题目场景数据
          let scenarioData = {}
          try {
            if (resultData.scenarioData) {
              scenarioData = JSON.parse(resultData.scenarioData)
            }
          } catch (e) {
            console.warn('解析scenarioData失败:', e)
          }

          // 设置考试基本信息
          this.examData = {
            title: resultData.examTitle,
            totalTime: resultData.totalTime,
            totalScore: resultData.maxScore,
            duration: resultData.totalTime
          }

          // 设置结果数据
          this.resultData = {
            totalScore: resultData.totalScore,
            precipitationScore: resultData.precipitationScore,
            weatherScore: resultData.weatherScore,
            ranking: resultData.ranking
          }

          // 设置用户答案数据
          if (resultData.userAnswer) {
            // 降水预报用户答案
            if (resultData.userAnswer.precipitationAnswer) {
              this.userPrecipitationData = resultData.userAnswer.precipitationAnswer.content || {}
            }

            // 天气预报用户答案
            if (resultData.userAnswer.weatherAnswer) {
              this.userWeatherAnswers = resultData.userAnswer.weatherAnswer.stations || {}
            }
          }

          // 设置标准答案数据 - 从scenarioData.answers获取
          if (scenarioData.answers) {
            // 天气预报标准答案从scenarioData.answers获取
            this.standardWeatherAnswers = scenarioData.answers || {}
          } else {
            // 如果scenarioData中没有答案，尝试从standardAnswer获取
            if (resultData.standardAnswer) {
              // 降水预报标准答案
              if (resultData.standardAnswer.precipitationAnswer) {
                this.standardPrecipitationData = resultData.standardAnswer.precipitationAnswer.content || {}
              }

              // 天气预报标准答案
              if (resultData.standardAnswer.weatherAnswer) {
                this.standardWeatherAnswers = resultData.standardAnswer.weatherAnswer.stations || {}
              }
            }
          }

          // 设置评分详情 - 使用gradingDetails数据
          if (resultData.gradingDetails && resultData.gradingDetails.elementScores) {
            // 从gradingDetails.elementScores获取第二部分评分详情
            const elementScores = resultData.gradingDetails.elementScores
            this.weatherScoring = {
              windForceScore: elementScores.windForce || 0,
              windDirectionScore: elementScores.windDirection || 0,
              // 分别保存最低和最高气温评分
              minTemperatureScore: elementScores.minTemperature || 0,
              maxTemperatureScore: elementScores.maxTemperature || 0,
              temperatureScore: (elementScores.minTemperature || 0) + (elementScores.maxTemperature || 0),
              precipitationScore: elementScores.precipitation || 0,
              disasterScore: elementScores.disasterWeather || 0
            }
          } else {
            // 如果没有gradingDetails，使用默认值
            this.weatherScoring = {
              windForceScore: 0,
              windDirectionScore: 0,
              minTemperatureScore: 0,
              maxTemperatureScore: 0,
              temperatureScore: 0,
              precipitationScore: 0,
              disasterScore: 0
            }
          }

          // 设置降水落区详细评分数据 - 调试数据结构
          console.log('完整的resultData:', resultData)
          console.log('gradingDetails:', resultData.gradingDetails)

          if (resultData.gradingDetails && resultData.gradingDetails.precipitationScoringDetails) {
            console.log('precipitationScoringDetails完整对象:', resultData.gradingDetails.precipitationScoringDetails)
            console.log('precipitationScoringDetails的所有键:', Object.keys(resultData.gradingDetails.precipitationScoringDetails))
            console.log('levelTSDetails:', resultData.gradingDetails.precipitationScoringDetails.levelTSDetails)

            if (resultData.gradingDetails.precipitationScoringDetails.levelTSDetails) {
              console.log('从gradingDetails.precipitationScoringDetails.levelTSDetails获取数据')

              // 初始化precipitationAreaDetails
              const precipitationScoringDetails = resultData.gradingDetails.precipitationScoringDetails
              // 根据您提供的数据结构，score和totalStations在同一级别
              console.log('precipitationScoringDetails中的score:', precipitationScoringDetails.score)
              console.log('precipitationScoringDetails中的finalScore:', precipitationScoringDetails.finalScore)
              console.log('precipitationScoringDetails中的totalStations:', precipitationScoringDetails.totalStations)

              // 使用score字段（根据您的数据结构图片）
              const finalScore = precipitationScoringDetails.score || precipitationScoringDetails.finalScore || 0

              console.log('最终使用的score值:', finalScore)

              this.precipitationAreaDetails = {
                score: finalScore,
                totalStations: precipitationScoringDetails.totalStations || 0,
                studentLevelTSDetails: {},
                mesoLevelTSDetails: {},
                skillScores: precipitationScoringDetails.skillScores || {},
                studentTSScores: precipitationScoringDetails.studentTSScores || {},
                cmaMesoTSScores: precipitationScoringDetails.cmaMesoTSScores || {},
                baseScores: precipitationScoringDetails.baseScores || {},
                weights: precipitationScoringDetails.weights || {}
              }

              console.log('设置后的score值:', this.precipitationAreaDetails.score)

              // 处理levelTSDetails数据
              this.processLevelTSDetails(resultData.gradingDetails.precipitationScoringDetails.levelTSDetails)
            } else {
              console.log('levelTSDetails不存在')
              this.precipitationAreaDetails = null
            }
          } else {
            console.log('未找到precipitationScoringDetails数据')
            console.log('gradingDetails结构:', resultData.gradingDetails)
            this.precipitationAreaDetails = null
          }





          // 保留原有的评分详情逻辑作为备用
          if (resultData.scoringDetails) {
            this.precipitationScoring = resultData.scoringDetails.precipitation || {}
            this.weatherScoringResult = resultData.scoringDetails.weatherResult || {}
          }

          // 设置分析信息
          this.precipitationAnalysis = resultData.precipitationAnalysis || {}

          // 保存scenarioData供其他方法使用
          this.scenarioData = scenarioData
        } else {
          throw new Error(response.msg || '获取考试结果失败')
        }
      } catch (error) {
        console.error('获取考试结果失败:', error)
        throw error
      }
    },

    // 加载题目数据
    async loadQuestionData() {
      try {
        // 使用已保存的scenarioData，如果没有则重新获取
        let scenarioData = this.scenarioData || {}
        let examDetail = {}

        if (!this.scenarioData) {
          const response = await getWeatherExamDetail({ id: this.examId })

          if (response.code === 0 && response.data) {
            examDetail = response.data

            // 解析题目数据
            try {
              if (examDetail.scenarioData) {
                scenarioData = JSON.parse(examDetail.scenarioData)
              }
            } catch (e) {
              console.warn('解析scenarioData失败:', e)
            }
          }
        }

        this.questionData = {
          id: examDetail.questionId,
          title: examDetail.title || '历史个例天气预报',
          content: examDetail.content || '根据提供的气象观测资料，对指定站点进行24小时天气预报。',

          // 第一部分相关信息
          precipitationContent: scenarioData.precipitationContent || '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
          precipitationRegion: scenarioData.precipitationRegion || 'region1',

          // 站点信息 - 从scenarioData获取
          stations: JSON.stringify(scenarioData.stations || []),

          // 预报信息
          forecastDate: examDetail.forecastDate || scenarioData.forecastStartDate || '2035年9月9日',
          forecastTime: scenarioData.precipitationForecastTime || examDetail.forecastTime || '08时',

          totalScore: examDetail.totalScore || 100
        }
      } catch (error) {
        console.warn('获取题目详情失败，使用默认数据:', error)

        // 使用默认题目数据
        this.questionData = {
          title: '历史个例天气预报',
          content: '根据提供的气象观测资料，对指定站点进行24小时天气预报。',
          precipitationContent: '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
          precipitationRegion: 'region1',
          forecastDate: '2035年9月9日',
          forecastTime: '08时',
          stations: JSON.stringify([]),
          totalScore: 100
        }
      }
    },

    // 获取区域标签
    getRegionLabel(regionValue) {
      const regionMap = {
        'region1': '区域一(华北区域)',
        'region2': '区域二(东北区域)',
        'region3': '区域三(长江中下游区域)',
        'region4': '区域四(华南区域)',
        'region5': '区域五(西南地区东部)',
        'region6': '区域六(青藏高原区域)',
        'region7': '区域七(新疆区域)',
        'region8': '区域八(西北地区东部区域)',
        'region9': '区域九(内蒙古区域)'
      }
      return regionMap[regionValue] || regionValue
    },

    // 格式化分数显示（3位小数）
    formatScore(score) {
      if (score === null || score === undefined) {
        return '0.000'
      }
      return Number(score).toFixed(3)
    },

    // 格式化分数显示（2位小数）
    formatScoreToTwoDecimals(score) {
      if (score === null || score === undefined) {
        return '0.00'
      }
      return Number(score).toFixed(2)
    },

    // 处理levelTSDetails数据，转换为现有的数据结构
    processLevelTSDetails(levelTSDetails) {
      if (!levelTSDetails || !Array.isArray(levelTSDetails)) {
        console.log('levelTSDetails不是有效数组:', levelTSDetails)
        return
      }

      console.log('处理levelTSDetails数据，共', levelTSDetails.length, '个量级')
      console.log('levelTSDetails完整数据:', levelTSDetails)

      // 初始化数据结构
      if (!this.precipitationAreaDetails.studentLevelTSDetails) {
        this.precipitationAreaDetails.studentLevelTSDetails = {}
      }
      if (!this.precipitationAreaDetails.mesoLevelTSDetails) {
        this.precipitationAreaDetails.mesoLevelTSDetails = {}
      }

      // 处理每个量级的数据
      levelTSDetails.forEach((levelDetail, index) => {
        console.log(`处理第${index}个量级数据:`, levelDetail)

        // 获取量级名称，优先从level字段获取，然后从mesoLevelDetails获取
        const levelName = levelDetail.level || levelDetail.mesoLevelDetails?.level || `量级${index}`

        console.log(`处理量级: ${levelName}`)

        // 处理考生TS评分数据
        if (levelDetail.studentTSStats) {
          const studentStats = levelDetail.studentTSStats
          const studentData = {
            level: levelName,
            correctA: studentStats.correctForecast || 0,
            wrongB: studentStats.wrongForecast || 0,
            missedC: studentStats.missedForecast || 0,
            total: (studentStats.correctForecast || 0) + (studentStats.wrongForecast || 0) + (studentStats.missedForecast || 0),
            tsScore: studentStats.tsScore || 0,
            formula: studentStats.formulaDescription || ''
          }

          // 晴雨量级需要特殊处理，包含正确预报无雨字段
          if (levelName === '晴雨' && studentStats.correctNoRainForecast !== undefined) {
            studentData.correctD = studentStats.correctNoRainForecast
            studentData.total = (studentStats.correctForecast || 0) + (studentStats.wrongForecast || 0) + (studentStats.missedForecast || 0) + (studentStats.correctNoRainForecast || 0)
          }

          this.precipitationAreaDetails.studentLevelTSDetails[levelName] = studentData
        }

        // 处理CMA-MESO TS评分数据
        if (levelDetail.mesoLevelDetails) {
          const mesoDetails = levelDetail.mesoLevelDetails
          const mesoData = {
            level: levelName,
            correctA: mesoDetails.correctForecast || 0,
            wrongB: mesoDetails.wrongForecast || 0,
            missedC: mesoDetails.missedForecast || 0,
            total: (mesoDetails.correctForecast || 0) + (mesoDetails.wrongForecast || 0) + (mesoDetails.missedForecast || 0),
            tsScore: mesoDetails.tsScore || 0,
            formula: mesoDetails.formulaDescription || ''
          }

          // 晴雨量级需要特殊处理，包含正确预报无雨字段
          if (levelName === '晴雨' && mesoDetails.correctNoRainForecast !== undefined) {
            mesoData.correctD = mesoDetails.correctNoRainForecast
            mesoData.total = (mesoDetails.correctForecast || 0) + (mesoDetails.wrongForecast || 0) + (mesoDetails.missedForecast || 0) + (mesoDetails.correctNoRainForecast || 0)
          }

          this.precipitationAreaDetails.mesoLevelTSDetails[levelName] = mesoData
        } else if (levelDetail.cmaMesoTSStats) {
          // 备用：如果没有mesoLevelDetails，尝试使用cmaMesoTSStats
          const mesoStats = levelDetail.cmaMesoTSStats
          const mesoData = {
            level: levelName,
            correctA: mesoStats.correctForecast || 0,
            wrongB: mesoStats.wrongForecast || 0,
            missedC: mesoStats.missedForecast || 0,
            total: (mesoStats.correctForecast || 0) + (mesoStats.wrongForecast || 0) + (mesoStats.missedForecast || 0),
            tsScore: mesoStats.tsScore || 0,
            formula: mesoStats.formulaDescription || ''
          }

          // 晴雨量级需要特殊处理，包含正确预报无雨字段
          if (levelName === '晴雨' && mesoStats.correctNoRainForecast !== undefined) {
            mesoData.correctD = mesoStats.correctNoRainForecast
            mesoData.total = (mesoStats.correctForecast || 0) + (mesoStats.wrongForecast || 0) + (mesoStats.missedForecast || 0) + (mesoStats.correctNoRainForecast || 0)
          }

          this.precipitationAreaDetails.mesoLevelTSDetails[levelName] = mesoData
        }

        // 从levelDetail获取技巧评分
        if (levelDetail.studentSkillScore !== undefined) {
          this.precipitationAreaDetails.skillScores[levelName] = levelDetail.studentSkillScore
        }
      })

      console.log('数据处理完成')
    },

    // 获取按照指定顺序排列的TS评分级别
    getSortedTSLevels(scoresObj) {
      if (!scoresObj || typeof scoresObj !== 'object') {
        return []
      }

      // 定义降水级别的显示顺序（从晴雨开始）
      const levelOrder = ['晴雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']

      // 获取对象中存在的级别，过滤掉微量降水
      const availableLevels = Object.keys(scoresObj).filter(level => level !== '微量降水')

      // 按照预定义顺序过滤和排序
      const sortedLevels = levelOrder.filter(level => availableLevels.includes(level))

      // 添加任何不在预定义顺序中但存在于数据中的级别（排除微量降水）
      const remainingLevels = availableLevels.filter(level => !levelOrder.includes(level) && level !== '微量降水')

      return [...sortedLevels, ...remainingLevels]
    },

    // 获取MESO量级列表
    getMesoLevels() {
      if (!this.precipitationAreaDetails || !this.precipitationAreaDetails.mesoLevelTSDetails) {
        return []
      }

      // 定义降水级别的显示顺序
      const levelOrder = ['小雨', '中雨', '大雨', '暴雨', '大暴雨']

      // 获取实际存在的级别
      const availableLevels = Object.keys(this.precipitationAreaDetails.mesoLevelTSDetails)

      // 按照预定义顺序过滤和排序
      return levelOrder.filter(level => availableLevels.includes(level))
    },

    // 获取考生量级列表
    getStudentLevels() {
      if (!this.precipitationAreaDetails || !this.precipitationAreaDetails.studentLevelTSDetails) {
        return []
      }

      // 定义降水级别的显示顺序（包含晴雨）
      const levelOrder = ['晴雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨']

      // 获取实际存在的级别
      const availableLevels = Object.keys(this.precipitationAreaDetails.studentLevelTSDetails)

      // 按照预定义顺序过滤和排序
      return levelOrder.filter(level => availableLevels.includes(level))
    },

    // 获取其他量级列表（除了晴雨）
    getOtherLevels() {
      if (!this.precipitationAreaDetails) {
        return []
      }

      // 定义降水级别的显示顺序（除了晴雨）
      const levelOrder = ['小雨', '中雨', '大雨', '暴雨', '大暴雨']

      // 获取实际存在的级别，优先从studentLevelTSDetails获取
      let availableLevels = []

      if (this.precipitationAreaDetails.studentLevelTSDetails) {
        availableLevels = Object.keys(this.precipitationAreaDetails.studentLevelTSDetails)
      } else if (this.precipitationAreaDetails.mesoLevelTSDetails) {
        availableLevels = Object.keys(this.precipitationAreaDetails.mesoLevelTSDetails)
      } else if (this.precipitationAreaDetails.baseScores) {
        availableLevels = Object.keys(this.precipitationAreaDetails.baseScores)
      }

      console.log('getOtherLevels - availableLevels:', availableLevels)

      // 按照预定义顺序过滤和排序，排除晴雨
      const filteredLevels = levelOrder.filter(level => availableLevels.includes(level))

      console.log('getOtherLevels - filteredLevels:', filteredLevels)

      return filteredLevels
    },

    // 根据量级获取权重
    getWeightByLevel(level) {
      // 定义各量级的权重
      const weights = {
        '晴雨': 0.1,
        '小雨': 0.2,
        '中雨': 0.25,
        '大雨': 0.25,
        '暴雨': 0.15,
        '大暴雨': 0.05
      }
      return weights[level] || 0
    },

    // 获取风力评分详情说明
    getWindForceScoreDetail() {
      const score = this.weatherScoring.windForceScore || 0
      if (score >= 1.0) {
        return '完全匹配'
      } else if (score >= 0.8) {
        return '相邻等级'
      } else if (score > 0) {
        return '部分正确'
      } else {
        return '预报错误'
      }
    },

    // 获取风向评分详情说明
    getWindDirectionScoreDetail() {
      const score = this.weatherScoring.windDirectionScore || 0
      if (score >= 1.0) {
        return '角度范围匹配'
      } else if (score >= 0.6) {
        return '相邻角度范围'
      } else if (score > 0) {
        return '部分正确'
      } else {
        return '预报错误'
      }
    },

    // 获取降水评分详情说明
    getPrecipitationScoreDetail() {
      const score = this.weatherScoring.precipitationScore || 0
      if (score >= 2.0) {
        return '完全匹配'
      } else if (score >= 1.2) {
        return '相邻等级/特殊关系'
      } else if (score > 0) {
        return '部分正确'
      } else {
        return '预报错误'
      }
    },

    // 获取灾害天气评分详情说明
    getDisasterWeatherScoreDetail() {
      const score = this.weatherScoring.disasterScore || 0
      if (score >= 2.0) {
        return '完全匹配'
      } else if (score > 0) {
        return '部分匹配'
      } else {
        return '预报错误'
      }
    },

    // 返回考试列表
    goBack() {
      this.$router.push('/my/weather')
    },

    // 显示站点详情弹窗
    async showStationDetails() {
      if (!this.precipitationAreaDetails) {
        this.$message.warning('暂无降水落区评分详情数据')
        return
      }

      this.loadingStationDetails = true
      this.stationDetailsVisible = true

      try {
        // 从当前已加载的数据中获取站点详情
        let stationDetails = null

        // 优先从precipitationAreaDetails中获取
        if (this.precipitationAreaDetails && this.precipitationAreaDetails.stationDetails) {
          stationDetails = this.precipitationAreaDetails.stationDetails
          console.log('从precipitationAreaDetails获取站点详情:', stationDetails.length, '个站点')
        }
        // 从gradingDetails.precipitationScoringDetails中获取
        else if (this.resultData.gradingDetails &&
            this.resultData.gradingDetails.precipitationScoringDetails &&
            this.resultData.gradingDetails.precipitationScoringDetails.stationDetails) {
          stationDetails = this.resultData.gradingDetails.precipitationScoringDetails.stationDetails
          console.log('从gradingDetails.precipitationScoringDetails获取站点详情:', stationDetails.length, '个站点')
        }
        // 从gradingDetails.detailResults.precipitationArea中获取
        else if (this.resultData.gradingDetails &&
            this.resultData.gradingDetails.detailResults &&
            this.resultData.gradingDetails.detailResults.precipitationArea &&
            this.resultData.gradingDetails.detailResults.precipitationArea.stationDetails) {
          stationDetails = this.resultData.gradingDetails.detailResults.precipitationArea.stationDetails
          console.log('从gradingDetails.detailResults.precipitationArea获取站点详情:', stationDetails.length, '个站点')
        }

        if (stationDetails && stationDetails.length > 0) {
          this.stationDetailsList = stationDetails
        } else {
          // 如果没有现成的站点详情，重新获取
          console.log('重新获取考试结果数据以获取站点详情')
          const response = await getWeatherExamResult({ examId: this.examId })
          if (response.code === 0 && response.data) {
            console.log('重新获取的数据:', response.data)

            // 检查各种可能的数据位置
            if (response.data.gradingDetails &&
                response.data.gradingDetails.precipitationScoringDetails &&
                response.data.gradingDetails.precipitationScoringDetails.stationDetails) {
              this.stationDetailsList = response.data.gradingDetails.precipitationScoringDetails.stationDetails
              console.log('从重新获取的precipitationScoringDetails中获取站点详情:', this.stationDetailsList.length, '个站点')
            } else if (response.data.gradingDetails &&
                response.data.gradingDetails.detailResults &&
                response.data.gradingDetails.detailResults.precipitationArea &&
                response.data.gradingDetails.detailResults.precipitationArea.stationDetails) {
              this.stationDetailsList = response.data.gradingDetails.detailResults.precipitationArea.stationDetails
              console.log('从重新获取的detailResults.precipitationArea中获取站点详情:', this.stationDetailsList.length, '个站点')
            } else {
              this.stationDetailsList = []
              this.$message.warning('无法获取站点详情数据，请检查评分是否完成')
              console.log('gradingDetails结构:', response.data.gradingDetails)
              if (response.data.gradingDetails && response.data.gradingDetails.detailResults) {
                console.log('detailResults的键:', Object.keys(response.data.gradingDetails.detailResults))
              }
            }
          } else {
            this.stationDetailsList = []
            this.$message.error('重新获取考试结果失败')
          }
        }

        // 初始化筛选列表
        this.filteredStationList = [...this.stationDetailsList]
      } catch (error) {
        console.error('获取站点详情失败:', error)
        this.$message.error('获取站点详情失败')
        this.stationDetailsList = []
        this.filteredStationList = []
      } finally {
        this.loadingStationDetails = false
      }
    },

    // 筛选站点
    filterStations() {
      let filtered = [...this.stationDetailsList]

      // 按实况等级筛选
      if (this.stationFilter.actualLevel) {
        filtered = filtered.filter(station => station.actualLevel === this.stationFilter.actualLevel)
      }

      // 按预报结果类型筛选
      if (this.stationFilter.resultType) {
        filtered = filtered.filter(station => {
          return (station.rainNoRainDetail && station.rainNoRainDetail.studentResultType === this.stationFilter.resultType) ||
                 (station.levelDetail && station.levelDetail.studentResultType === this.stationFilter.resultType)
        })
      }

      // 按站点ID筛选
      if (this.stationFilter.stationId) {
        filtered = filtered.filter(station =>
          station.stationId.toString().includes(this.stationFilter.stationId)
        )
      }

      this.filteredStationList = filtered
    },

    // 获取有降水站点数量
    getRainStationCount() {
      return this.stationDetailsList.filter(station => station.actualLevel !== '无雨').length
    },

    // 获取预报正确站点数量
    getCorrectStationCount() {
      return this.stationDetailsList.filter(station => {
        return (station.rainNoRainDetail && (station.rainNoRainDetail.studentResultType === '正确A' || station.rainNoRainDetail.studentResultType === '正确D')) ||
               (station.levelDetail && station.levelDetail.studentResultType === '正确A')
      }).length
    },

    // 获取站点预报正确率
    getStationAccuracy() {
      if (this.stationDetailsList.length === 0) return 0
      const correctCount = this.getCorrectStationCount()
      return Math.round((correctCount / this.stationDetailsList.length) * 100)
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        '无雨': '',
        '小雨': 'info',
        '中雨': 'warning',
        '大雨': 'danger',
        '暴雨': 'danger',
        '大暴雨': 'danger',
        '微量降水': 'success'
      }
      return typeMap[level] || ''
    },

    // 获取结果标签类型
    getResultTagType(resultType) {
      const typeMap = {
        '正确A': 'success',
        '正确D': 'success',
        '空报B': 'warning',
        '漏报C': 'danger',
        '错误B': 'danger'
      }
      return typeMap[resultType] || ''
    },

    // 导出站点详情
    exportStationDetails() {
      if (!this.filteredStationList || this.filteredStationList.length === 0) {
        this.$message.warning('没有可导出的数据')
        return
      }

      // 构建CSV内容
      const headers = [
        '站点ID', '经度', '纬度', '实况降水量(mm)', '实况等级',
        '考生预报降水量(mm)', '考生预报等级', 'CMA-MESO预报降水量(mm)', 'CMA-MESO预报等级',
        '晴雨-考生结果', '晴雨-CMA结果', '量级-考生结果', '量级-CMA结果', '特殊规则说明'
      ]

      const csvContent = [
        headers.join(','),
        ...this.filteredStationList.map(station => [
          station.stationId,
          station.longitude.toFixed(4),
          station.latitude.toFixed(4),
          station.actualPrecipitation.toFixed(1),
          station.actualLevel,
          station.studentForecastPrecipitation.toFixed(1),
          station.studentForecastLevel,
          station.cmaMesoForecastPrecipitation.toFixed(1),
          station.cmaMesoForecastLevel,
          station.rainNoRainDetail ? station.rainNoRainDetail.studentResultType : '',
          station.rainNoRainDetail ? station.rainNoRainDetail.cmaMesoResultType : '',
          station.levelDetail ? station.levelDetail.studentResultType : '',
          station.levelDetail ? station.levelDetail.cmaMesoResultType : '',
          station.levelDetail ? (station.levelDetail.specialRuleNote || '') : ''
        ].join(','))
      ].join('\n')

      // 下载CSV文件
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `站点详情_${this.examId}_${new Date().getTime()}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success('导出成功')
    }
  }
}
</script>

<style scoped>
.weather-exam-result {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
}

.weather-exam-result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.result-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  color: #2c3e50;
  padding: 30px;
  border-radius: 20px;
  margin-bottom: 25px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.result-title h2 {
  margin: 0 0 15px 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.exam-meta {
  display: flex;
  gap: 25px;
  align-items: center;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 500;
  padding: 8px 16px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 25px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.meta-item i {
  font-size: 16px;
  color: #667eea;
}

.meta-item.score-display {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  border: 1px solid rgba(103, 194, 58, 0.3);
  font-weight: 600;
}

.meta-item.score-display i {
  color: #ffffff;
}

.final-score {
  font-size: 20px;
  font-weight: 800;
  margin: 0 4px;
}

.score-summary {
  flex-shrink: 0;
}

.score-card {
  display: flex;
  gap: 20px;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.score-item {
  text-align: center;
}

.score-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
  font-weight: 500;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 2px;
}

.score-value.primary { color: #409eff; }
.score-value.success { color: #67c23a; }
.score-value.info { color: #909399; }

.score-unit {
  font-size: 12px;
  color: #909399;
}

.score-divider {
  width: 1px;
  height: 40px;
  background: #e4e7ed;
}

.result-content {
  position: relative;
  z-index: 1;
}

.result-section {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
}

.section-header {
  padding: 20px 25px 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.section-title h3 {
  margin: 0;
  color: white;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title h3::before {
  content: '📊';
  font-size: 24px;
}

.section-score .el-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(4px);
}

.precipitation-result-section,
.weather-result-section {
  padding: 25px;
}

.question-info {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  align-items: flex-start;
}

.question-content {
  flex: 2;
  padding: 20px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 16px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.1);
}

.question-content h4 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-content h4::before {
  content: '📝';
  font-size: 18px;
}

.question-content p {
  margin: 0;
  color: #37474f;
  line-height: 1.7;
  font-size: 14px;
  font-weight: 500;
}

.region-info {
  flex: 1;
  padding: 20px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 16px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  box-shadow: 0 4px 20px rgba(103, 194, 58, 0.1);
}

.region-info h4 {
  margin: 0 0 12px 0;
  color: #388e3c;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-info h4::before {
  content: '🗺️';
  font-size: 18px;
}

.precipitation-result-display {
  margin-top: 20px;
}

.result-tabs-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 15px;
}

.precipitation-display-container {
  margin-top: 15px;
  padding: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
}

.comparison-analysis {
  padding: 20px;
  background: linear-gradient(135deg, #fff9e6 0%, #fff3d3 100%);
  border-radius: 12px;
  margin-top: 15px;
}

.analysis-summary h4 {
  margin: 0 0 15px 0;
  color: #e6a23c;
  font-size: 18px;
  font-weight: 600;
}

.analysis-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(230, 162, 60, 0.2);
}

.analysis-label {
  font-weight: 600;
  color: #606266;
}

.analysis-value {
  color: #e6a23c;
  font-weight: 600;
}

.result-table-container {
  margin-top: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.scoring-details {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.scoring-breakdown {
  padding: 25px;
}

.scoring-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.scoring-part h4 {
  margin: 0 0 20px 0;
  color: #1976d2;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(25, 118, 210, 0.2);
}

.scoring-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scoring-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  border: 1px solid rgba(25, 118, 210, 0.1);
  transition: all 0.3s ease;
}

.scoring-item:hover {
  background: rgba(25, 118, 210, 0.05);
  border-color: rgba(25, 118, 210, 0.2);
}

.item-name {
  font-weight: 500;
  color: #606266;
}

.item-score {
  font-weight: 600;
  color: #1976d2;
  font-size: 15px;
}

.item-detail {
  font-size: 12px;
  color: #909399;
  font-style: italic;
  margin-left: 8px;
}

/* 评分要素信息样式 */
.scoring-elements-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.elements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.element-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.element-name {
  font-weight: 500;
  color: #606266;
  font-size: 13px;
}

.element-score {
  font-weight: 600;
  color: #1976d2;
  font-size: 13px;
}

/* 降水落区详细评分样式 */
.precipitation-area-details {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-section h5::before {
  content: '📊';
  font-size: 18px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.detail-label {
  font-weight: 500;
  color: #606266;
}

.detail-value {
  font-weight: 600;
  color: #1976d2;
}

.score-highlight {
  color: #e6a23c;
  font-size: 16px;
  font-weight: 700;
}

.ts-scores-section {
  margin-bottom: 20px;
}

.ts-scores-section h5 {
  margin: 0 0 15px 0;
  color: #67c23a;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ts-scores-section h5::before {
  content: '🎯';
  font-size: 18px;
}

.ts-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.ts-column {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.ts-column h6 {
  margin: 0 0 12px 0;
  color: #67c23a;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(103, 194, 58, 0.2);
}

.ts-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ts-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: rgba(103, 194, 58, 0.05);
  border-radius: 6px;
}

.ts-level {
  font-weight: 500;
  color: #606266;
  font-size: 16px;
}

.ts-score {
  font-weight: 600;
  color: #67c23a;
  font-size: 16px;
  font-family: 'Courier New', monospace;
}

.base-skill-scores {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.score-column {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(230, 162, 60, 0.2);
}

.score-column h6 {
  margin: 0 0 12px 0;
  color: #e6a23c;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(230, 162, 60, 0.2);
}

.score-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.score-item-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: rgba(230, 162, 60, 0.05);
  border-radius: 4px;
}

.score-level {
  font-weight: 500;
  color: #606266;
  font-size: 15px;
}

.score-value-small {
  font-weight: 600;
  color: #e6a23c;
  font-size: 15px;
  font-family: 'Courier New', monospace;
}

/* MESO详细记录样式 */
.meso-details-section {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

/* 考生详细记录样式 */
.student-details-section {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
}

.meso-details-section h5 {
  margin: 0 0 20px 0;
  color: #409eff;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.meso-details-section h5::before {
  content: '📊';
  font-size: 20px;
}

.student-details-section h5 {
  margin: 0 0 20px 0;
  color: #67c23a;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.student-details-section h5::before {
  content: '👤';
  font-size: 20px;
}

.meso-rain-details, .meso-level-details {
  margin-bottom: 20px;
}

.meso-rain-details h6, .meso-level-details h6 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(64, 158, 255, 0.2);
}

.student-rain-details, .student-level-details {
  margin-bottom: 20px;
}

.student-rain-details h6, .student-level-details h6 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(103, 194, 58, 0.2);
}

.meso-summary, .level-summary {
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #409eff;
}

.student-summary {
  background: rgba(103, 194, 58, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #67c23a;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.summary-value {
  font-weight: 600;
  color: #409eff;
  font-size: 16px;
  font-family: 'Courier New', monospace;
}

.summary-formula {
  font-weight: 500;
  color: #67c23a;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  background: rgba(103, 194, 58, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.meso-stats, .level-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.student-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(64, 158, 255, 0.15);
}

.stat-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #409eff;
  font-size: 15px;
  font-family: 'Courier New', monospace;
}

.meso-level-tabs-container {
  margin-top: 10px;
}

.meso-level-tabs-container .el-tabs__header {
  margin-bottom: 15px;
}

.meso-level-tabs-container .el-tabs__item {
  color: #409eff;
  font-weight: 500;
}

.meso-level-tabs-container .el-tabs__item.is-active {
  color: #409eff;
  font-weight: 600;
}

.meso-level-content {
  padding: 15px 0;
}

.scoring-summary {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.scoring-summary h6 {
  margin: 0 0 10px 0;
  color: #1976d2;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.scoring-summary h6::before {
  content: '📝';
  font-size: 16px;
}

.summary-text {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 15px;
  background: rgba(25, 118, 210, 0.05);
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #1976d2;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0;
  position: relative;
  z-index: 1;
}

.result-actions .el-button {
  min-width: 140px;
  height: 45px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-actions .el-button:not(.el-button--primary) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.result-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .exam-meta {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .score-card {
    flex-direction: column;
    gap: 15px;
  }

  .score-divider {
    width: 100%;
    height: 1px;
  }

  .question-info {
    flex-direction: column;
    gap: 15px;
  }

  .scoring-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  /* 降水落区详细评分响应式 */
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .ts-comparison {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .base-skill-scores {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  /* MESO详细记录响应式 */
  .meso-stats, .level-stats {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .meso-details-section {
    padding: 15px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .summary-formula {
    font-size: 12px;
    word-break: break-all;
  }

  /* 评分要素信息响应式 */
  .elements-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .element-info {
    padding: 6px 10px;
  }

  .element-name,
  .element-score {
    font-size: 12px;
  }

  .item-detail {
    display: block;
    margin-left: 0;
    margin-top: 4px;
    font-size: 11px;
  }
}

/* 站点详情按钮样式 */
.station-details-section {
  margin-top: 20px;
  text-align: center;
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  border: 1px dashed rgba(102, 126, 234, 0.2);
}

/* 站点详情弹窗样式 */
.station-details-dialog .el-dialog {
  border-radius: 12px;
}

.station-details-dialog .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px;
}

.station-details-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
}

.station-details-content {
  padding: 20px;
}

.station-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.station-filters {
  margin-bottom: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.station-details-table {
  border-radius: 8px;
  overflow: hidden;
}

.precipitation-info {
  text-align: center;
}

.precipitation-value {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.contribution-text {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.special-rule-note {
  font-size: 10px;
  color: #f56c6c;
  margin-top: 2px;
  line-height: 1.2;
}

.no-level-scoring {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.no-station-data {
  padding: 40px;
  text-align: center;
}

/* 集成量级评分样式 */
.integrated-level-scoring {
  margin-top: 20px;
}

.integrated-level-scoring h6 {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e8ed;
}

.integrated-level-tabs-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.integrated-level-content {
  padding: 20px;
}

/* TS评分对比卡片样式 */
.ts-comparison-cards {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.ts-score-card {
  flex: 1;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.ts-score-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 考生评分卡片 */
.student-card {
  background: linear-gradient(135deg, #a8b5d1 0%, #9fa8c3 100%);
  color: #2c3e50;
  border: 1px solid #d5dae2;
}

.student-card .card-header {
  background: rgba(255, 255, 255, 0.3);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
}

.student-card .card-header h7 {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #2c3e50;
}

.student-card .card-content {
  padding: 20px;
}

.student-card .main-score {
  margin-bottom: 15px;
}

.student-card .score-label {
  font-size: 14px;
  color: #34495e;
  margin-right: 8px;
  font-weight: 500;
}

.student-card .score-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.student-card .student-details {
  margin: 15px 0;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.4);
}

.student-card .detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.student-card .detail-label {
  color: #34495e;
  font-weight: 500;
}

.student-card .detail-value {
  font-weight: 600;
  color: #2c3e50;
}

.student-card .formula-item {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.student-card .formula-label {
  display: block;
  font-size: 12px;
  color: #34495e;
  margin-bottom: 4px;
  font-weight: 500;
}

.student-card .formula-value {
  font-size: 11px;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.3);
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
  color: #2c3e50;
}

.student-card .total-score {
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.4);
}

.student-card .total-score .score-value.highlight {
  font-size: 20px;
  font-weight: 700;
  color: #e67e22;
}

/* MESO评分卡片 */
.meso-card {
  background: linear-gradient(135deg, #d4b5d4 0%, #c9a9c9 100%);
  color: #2c3e50;
  border: 1px solid #d5c7d5;
}

.meso-card .card-header {
  background: rgba(255, 255, 255, 0.3);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
}

.meso-card .card-header h7 {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #2c3e50;
}

.meso-card .card-content {
  padding: 20px;
}

.meso-card .main-score {
  margin-bottom: 20px;
}

.meso-card .score-label {
  font-size: 14px;
  color: #34495e;
  margin-right: 8px;
  font-weight: 500;
}

.meso-card .score-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.meso-card .meso-details {
  border-top: 1px solid rgba(255, 255, 255, 0.4);
  padding-top: 15px;
}

.meso-card .detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.meso-card .detail-label {
  color: #34495e;
  font-weight: 500;
}

.meso-card .detail-value {
  font-weight: 600;
  color: #2c3e50;
}

.meso-card .formula-item {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.meso-card .formula-label {
  display: block;
  font-size: 12px;
  color: #34495e;
  margin-bottom: 4px;
  font-weight: 500;
}

.meso-card .formula-value {
  font-size: 11px;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.3);
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
  color: #2c3e50;
}

.card-title {
  margin: 0;
  color: #1565c0;
  font-size: 15px;
  font-weight: 600;
}
</style>
