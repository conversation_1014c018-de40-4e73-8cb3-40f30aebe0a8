<template>
  <div class="upload-test-container">
    <el-card class="test-card">
      <div slot="header" class="card-header">
        <span>历史个例文件上传接口测试</span>
      </div>

      <el-row :gutter="20">
        <!-- MICAPS文件上传测试 -->
        <el-col :span="8">
          <div class="upload-section">
            <h4>MICAPS文件上传</h4>
            <el-upload
              :action="micapsUploadUrl"
              :headers="uploadHeaders"
              :on-success="handleMicapsUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeMicapsUpload"
              drag
              accept=".000,.024,.dat,.txt,.nc,.grib,.grib2,.cma"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将MICAPS文件拖到此处，或<em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">
                支持格式：.000, .024, .dat, .txt, .nc, .grib, .grib2, .cma
              </div>
            </el-upload>

            <div v-if="micapsResult" class="result-info">
              <h5>上传结果：</h5>
              <pre>{{ JSON.stringify(micapsResult, null, 2) }}</pre>
            </div>
          </div>
        </el-col>

        <!-- 实况文件上传测试 -->
        <el-col :span="8">
          <div class="upload-section">
            <h4>实况文件上传</h4>
            <el-upload
              :action="observationUploadUrl"
              :headers="uploadHeaders"
              :on-success="handleObservationUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeObservationUpload"
              drag
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将实况文件拖到此处，或<em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">
                支持所有格式，大小限制50MB
              </div>
            </el-upload>

            <div v-if="observationResult" class="result-info">
              <h5>上传结果：</h5>
              <pre>{{ JSON.stringify(observationResult, null, 2) }}</pre>
            </div>
          </div>
        </el-col>

        <!-- 数据文件上传测试 -->
        <el-col :span="8">
          <div class="upload-section">
            <h4>数据文件上传</h4>
            <el-upload
              :action="dataUploadUrl"
              :headers="uploadHeaders"
              :on-success="handleDataUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeDataUpload"
              drag
              accept=".zip,.rar,.7z,.tar,.gz,.bz2"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将数据文件拖到此处，或<em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">
                支持格式：.zip, .rar, .7z, .tar, .gz, .bz2
              </div>
            </el-upload>

            <div v-if="dataResult" class="result-info">
              <h5>上传结果：</h5>
              <pre>{{ JSON.stringify(dataResult, null, 2) }}</pre>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 通用上传接口对比测试 -->
      <el-divider>通用上传接口对比</el-divider>

      <el-row>
        <el-col :span="12">
          <div class="upload-section">
            <h4>通用上传接口</h4>
            <el-upload
              :action="commonUploadUrl"
              :headers="uploadHeaders"
              :on-success="handleCommonUploadSuccess"
              :on-error="handleUploadError"
              drag
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                使用通用上传接口
              </div>
            </el-upload>

            <div v-if="commonResult" class="result-info">
              <h5>上传结果：</h5>
              <pre>{{ JSON.stringify(commonResult, null, 2) }}</pre>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'UploadTest',
  data() {
    return {
      // 专用上传接口
      micapsUploadUrl: `${process.env.VUE_APP_BASE_API}/exam/api/weather/case/upload/micaps`,
      observationUploadUrl: `${process.env.VUE_APP_BASE_API}/exam/api/weather/case/upload/observation`,
      dataUploadUrl: `${process.env.VUE_APP_BASE_API}/exam/api/weather/case/upload/data`,

      // 通用上传接口
      commonUploadUrl: `${process.env.VUE_APP_BASE_API}/common/api/file/upload`,

      // 上传头信息
      uploadHeaders: {
        'token': getToken()
      },

      // 上传结果
      micapsResult: null,
      observationResult: null,
      dataResult: null,
      commonResult: null
    }
  },

  methods: {
    // MICAPS文件上传前验证
    beforeMicapsUpload(file) {
      const isValidFormat = /\.(000|024|dat|txt|nc|grib|grib2|cma)$/i.test(file.name)
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isValidFormat) {
        this.$message.error('MICAPS文件格式不正确！')
        return false
      }
      if (!isLt100M) {
        this.$message.error('MICAPS文件大小不能超过100MB！')
        return false
      }
      return true
    },

    // 实况文件上传前验证
    beforeObservationUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isLt50M) {
        this.$message.error('实况文件大小不能超过50MB！')
        return false
      }
      return true
    },

    // 数据文件上传前验证
    beforeDataUpload(file) {
      const isValidFormat = /\.(zip|rar|7z|tar|gz|bz2)$/i.test(file.name)
      const isLt500M = file.size / 1024 / 1024 < 500

      if (!isValidFormat) {
        this.$message.error('数据文件格式不正确！')
        return false
      }
      if (!isLt500M) {
        this.$message.error('数据文件大小不能超过500MB！')
        return false
      }
      return true
    },

    // 上传成功回调
    handleMicapsUploadSuccess(response, file) {
      this.micapsResult = response
      if (response.code === 0) {
        this.$message.success('MICAPS文件上传成功')
      } else {
        this.$message.error(response.message || 'MICAPS文件上传失败')
      }
    },

    handleObservationUploadSuccess(response, file) {
      this.observationResult = response
      if (response.code === 0) {
        this.$message.success('实况文件上传成功')
      } else {
        this.$message.error(response.message || '实况文件上传失败')
      }
    },

    handleDataUploadSuccess(response, file) {
      this.dataResult = response
      if (response.code === 0) {
        this.$message.success('数据文件上传成功')
      } else {
        this.$message.error(response.message || '数据文件上传失败')
      }
    },

    handleCommonUploadSuccess(response, file) {
      this.commonResult = response
      if (response.code === 0) {
        this.$message.success('通用接口上传成功')
      } else {
        this.$message.error(response.msg || '通用接口上传失败')
      }
    },

    // 上传失败回调
    handleUploadError(err, file) {
      console.error('上传失败：', err)
      this.$message.error('文件上传失败')
    }
  }
}
</script>

<style scoped>
.upload-test-container {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.upload-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.upload-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.result-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.result-info h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.result-info pre {
  margin: 0;
  font-size: 12px;
  color: #909399;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
