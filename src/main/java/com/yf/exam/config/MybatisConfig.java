package com.yf.exam.config;

import com.yf.exam.aspect.mybatis.QueryInterceptor;
import com.yf.exam.aspect.mybatis.UpdateInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

import java.util.Map;

/**
 * Mybatis过滤器配置
 * 注意：必须按顺序进行配置，否则容易出现业务异常
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan("com.yf.exam.modules.**.mapper")
public class MybatisConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        try {
            // 从 Spring 上下文获取 SqlSessionFactory
            SqlSessionFactory sqlSessionFactory = event.getApplicationContext().getBean(SqlSessionFactory.class);

            if (sqlSessionFactory != null) {
                // 手动注册 CustomJacksonTypeHandler
                sqlSessionFactory.getConfiguration().getTypeHandlerRegistry()
                    .register(Map.class, JdbcType.VARCHAR, CustomJacksonTypeHandler.class);
                sqlSessionFactory.getConfiguration().getTypeHandlerRegistry()
                    .register(Map.class, JdbcType.LONGVARCHAR, CustomJacksonTypeHandler.class);
                sqlSessionFactory.getConfiguration().getTypeHandlerRegistry()
                    .register(Map.class, JdbcType.CLOB, CustomJacksonTypeHandler.class);

                log.info("CustomJacksonTypeHandler 注册成功");
            }

        } catch (Exception e) {
            log.error("TypeHandler 注册失败", e);
        }
    }

    /**
     * 数据查询过滤器
     */
    @Bean
    public QueryInterceptor queryInterceptor() {
        QueryInterceptor query =  new QueryInterceptor();
        query.setLimit(-1L);
        return query;
    }

    /**
     * 插入数据过滤器
     */
    @Bean
    public UpdateInterceptor updateInterceptor() {
        return new UpdateInterceptor();
    }


}