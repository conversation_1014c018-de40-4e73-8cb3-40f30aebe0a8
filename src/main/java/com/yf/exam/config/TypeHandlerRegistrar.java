package com.yf.exam.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * TypeHandler 注册器
 * 在应用启动完成后注册 CustomJacksonTypeHandler
 * 避免循环依赖问题
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Component
public class TypeHandlerRegistrar {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 应用启动完成后注册 TypeHandler
     */
    @EventListener(ApplicationReadyEvent.class)
    public void registerTypeHandlers() {
        try {
            if (sqlSessionFactory != null) {
                // 手动注册 CustomJacksonTypeHandler
                sqlSessionFactory.getConfiguration().getTypeHandlerRegistry()
                    .register(Map.class, JdbcType.VARCHAR, CustomJacksonTypeHandler.class);
                sqlSessionFactory.getConfiguration().getTypeHandlerRegistry()
                    .register(Map.class, JdbcType.LONGVARCHAR, CustomJacksonTypeHandler.class);
                sqlSessionFactory.getConfiguration().getTypeHandlerRegistry()
                    .register(Map.class, JdbcType.CLOB, CustomJacksonTypeHandler.class);

                log.debug("CustomJacksonTypeHandler 备用注册完成");
            }

        } catch (Exception e) {
            log.error("TypeHandler 备用注册失败", e);
        }
    }
}
