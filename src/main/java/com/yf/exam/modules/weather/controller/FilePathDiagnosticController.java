package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.util.WeatherFilePathUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 文件路径诊断控制器
 * 用于诊断Ubuntu环境下的文件路径问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/diagnostic")
@Api(tags = "文件路径诊断")
public class FilePathDiagnosticController extends BaseController {

    private static final String WEATHER_BASE_DIR = "/data/upload/weather/";

    /**
     * 诊断文件路径问题
     */
    @ApiOperation(value = "诊断文件路径")
    @PostMapping("/diagnose-path")
    public ApiRest<Map<String, Object>> diagnosePath(@RequestParam("filePath") String filePath) {
        Map<String, Object> result = new HashMap<>();
        List<String> messages = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();

        log.info("开始诊断文件路径: {}", filePath);

        try {
            // 1. 检查输入参数
            if (filePath == null || filePath.trim().isEmpty()) {
                messages.add("❌ 文件路径为空");
                suggestions.add("请提供有效的文件路径");
                result.put("success", false);
                result.put("messages", messages);
                result.put("suggestions", suggestions);
                return super.success(result);
            }

            // 2. 检查系统信息
            String osName = System.getProperty("os.name");
            String userDir = System.getProperty("user.dir");
            String userName = System.getProperty("user.name");
            
            messages.add("📋 系统信息:");
            messages.add("  - 操作系统: " + osName);
            messages.add("  - 当前用户: " + userName);
            messages.add("  - 工作目录: " + userDir);

            // 3. 检查基础目录
            File baseDir = new File(WEATHER_BASE_DIR);
            messages.add("📁 基础目录检查:");
            messages.add("  - 基础路径: " + WEATHER_BASE_DIR);
            messages.add("  - 目录存在: " + (baseDir.exists() ? "✅" : "❌"));
            messages.add("  - 可读权限: " + (baseDir.canRead() ? "✅" : "❌"));
            messages.add("  - 可写权限: " + (baseDir.canWrite() ? "✅" : "❌"));

            if (!baseDir.exists()) {
                suggestions.add("创建基础目录: sudo mkdir -p " + WEATHER_BASE_DIR);
                suggestions.add("设置目录权限: sudo chown -R $USER:$USER /data/upload");
                suggestions.add("设置目录权限: sudo chmod -R 755 /data/upload");
            }

            // 4. 检查子目录
            String[] subDirs = {"micaps", "observation", "data", "precipitation-area"};
            messages.add("📂 子目录检查:");
            for (String subDir : subDirs) {
                File dir = new File(WEATHER_BASE_DIR + subDir);
                messages.add("  - " + subDir + ": " + (dir.exists() ? "✅" : "❌"));
                if (!dir.exists()) {
                    suggestions.add("创建子目录: sudo mkdir -p " + WEATHER_BASE_DIR + subDir);
                }
            }

            // 5. 路径解析测试
            messages.add("🔍 路径解析测试:");
            List<String> testPaths = generateTestPaths(filePath);
            boolean fileFound = false;
            String foundPath = null;

            for (String testPath : testPaths) {
                File testFile = new File(testPath);
                boolean exists = testFile.exists();
                messages.add("  - 测试路径: " + testPath + " -> " + (exists ? "✅ 找到" : "❌ 未找到"));
                
                if (exists && !fileFound) {
                    fileFound = true;
                    foundPath = testPath;
                }
            }

            // 6. 使用工具类测试
            messages.add("🛠️ 工具类测试:");
            String resolvedPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
            if (resolvedPath != null) {
                messages.add("  - 工具类解析结果: ✅ " + resolvedPath);
                if (!fileFound) {
                    fileFound = true;
                    foundPath = resolvedPath;
                }
            } else {
                messages.add("  - 工具类解析结果: ❌ 未找到");
            }

            // 7. 生成建议
            if (!fileFound) {
                suggestions.add("文件未找到，请检查:");
                suggestions.add("1. 文件是否已正确上传");
                suggestions.add("2. 文件路径是否正确");
                suggestions.add("3. 目录权限是否正确");
                suggestions.add("4. 运行 ubuntu-setup.sh 脚本设置目录");
            } else {
                messages.add("✅ 文件找到: " + foundPath);
            }

            result.put("success", fileFound);
            result.put("foundPath", foundPath);
            result.put("messages", messages);
            result.put("suggestions", suggestions);
            result.put("testPaths", testPaths);

            return super.success(result);

        } catch (Exception e) {
            log.error("诊断文件路径时出错", e);
            messages.add("❌ 诊断过程中出错: " + e.getMessage());
            result.put("success", false);
            result.put("messages", messages);
            result.put("suggestions", suggestions);
            return super.success(result);
        }
    }

    /**
     * 检查目录结构
     */
    @ApiOperation(value = "检查目录结构")
    @GetMapping("/check-directories")
    public ApiRest<Map<String, Object>> checkDirectories() {
        Map<String, Object> result = new HashMap<>();
        List<String> messages = new ArrayList<>();

        messages.add("📁 检查目录结构:");

        try {
            Path basePath = Paths.get(WEATHER_BASE_DIR);
            if (Files.exists(basePath)) {
                messages.add("✅ 基础目录存在: " + WEATHER_BASE_DIR);
                
                // 检查子目录
                String[] subDirs = {"micaps", "observation", "data", "precipitation-area"};
                for (String subDir : subDirs) {
                    Path subPath = basePath.resolve(subDir);
                    if (Files.exists(subPath)) {
                        messages.add("✅ 子目录存在: " + subDir);
                        
                        // 列出文件
                        try {
                            long fileCount = Files.list(subPath).count();
                            messages.add("  - 文件数量: " + fileCount);
                        } catch (Exception e) {
                            messages.add("  - 无法读取文件列表: " + e.getMessage());
                        }
                    } else {
                        messages.add("❌ 子目录不存在: " + subDir);
                    }
                }
                result.put("success", true);
            } else {
                messages.add("❌ 基础目录不存在: " + WEATHER_BASE_DIR);
                result.put("success", false);
            }
        } catch (Exception e) {
            messages.add("❌ 检查目录时出错: " + e.getMessage());
            result.put("success", false);
        }

        result.put("messages", messages);
        return super.success(result);
    }

    /**
     * 生成测试路径列表
     */
    private List<String> generateTestPaths(String filePath) {
        List<String> testPaths = new ArrayList<>();

        // 1. 原始路径
        testPaths.add(filePath);

        // 2. 如果是相对路径，尝试不同的基础路径
        if (!filePath.startsWith("/")) {
            // 天气模块路径
            if (filePath.startsWith("weather/")) {
                testPaths.add(WEATHER_BASE_DIR + filePath.substring("weather/".length()));
            } else {
                testPaths.add(WEATHER_BASE_DIR + filePath);
            }

            // 工作目录路径
            testPaths.add(System.getProperty("user.dir") + "/" + filePath);

            // 通用上传目录
            testPaths.add("/data/upload/" + filePath);
        }

        return testPaths;
    }

    /**
     * 列出目录下的所有文件
     */
    @ApiOperation(value = "列出目录文件")
    @GetMapping("/list-files")
    public ApiRest<Map<String, Object>> listFiles(@RequestParam(value = "subDir", required = false) String subDir) {
        Map<String, Object> result = new HashMap<>();
        List<String> messages = new ArrayList<>();

        try {
            String targetDir = WEATHER_BASE_DIR;
            if (subDir != null && !subDir.trim().isEmpty()) {
                targetDir += subDir + "/";
            }

            File dir = new File(targetDir);
            if (!dir.exists()) {
                messages.add("❌ 目录不存在: " + targetDir);
                result.put("success", false);
            } else {
                messages.add("📁 目录: " + targetDir);
                File[] files = dir.listFiles();
                if (files != null && files.length > 0) {
                    messages.add("📄 文件列表:");
                    for (File file : files) {
                        String fileInfo = String.format("  - %s (%s, %d bytes)", 
                            file.getName(), 
                            file.isDirectory() ? "目录" : "文件",
                            file.length());
                        messages.add(fileInfo);
                    }
                } else {
                    messages.add("📄 目录为空");
                }
                result.put("success", true);
            }
        } catch (Exception e) {
            messages.add("❌ 列出文件时出错: " + e.getMessage());
            result.put("success", false);
        }

        result.put("messages", messages);
        return super.success(result);
    }
}
