package com.yf.exam.modules.weather.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件路径诊断工具类
 * 用于诊断Ubuntu环境下的文件路径问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
public class FilePathDiagnosticUtil {

    private static final String WEATHER_BASE_DIR = "/data/upload/weather/";

    /**
     * 诊断文件路径问题
     * 
     * @param filePath 要诊断的文件路径
     * @return 诊断结果
     */
    public static Map<String, Object> diagnoseFilePath(String filePath) {
        Map<String, Object> result = new HashMap<>();
        List<String> messages = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();

        log.info("开始诊断文件路径: {}", filePath);

        // 1. 检查输入参数
        if (!StringUtils.hasText(filePath)) {
            messages.add("❌ 文件路径为空");
            suggestions.add("请提供有效的文件路径");
            result.put("success", false);
            result.put("messages", messages);
            result.put("suggestions", suggestions);
            return result;
        }

        // 2. 检查系统信息
        String osName = System.getProperty("os.name");
        String userDir = System.getProperty("user.dir");
        String userName = System.getProperty("user.name");
        
        messages.add("📋 系统信息:");
        messages.add("  - 操作系统: " + osName);
        messages.add("  - 当前用户: " + userName);
        messages.add("  - 工作目录: " + userDir);

        // 3. 检查基础目录
        File baseDir = new File(WEATHER_BASE_DIR);
        messages.add("📁 基础目录检查:");
        messages.add("  - 基础路径: " + WEATHER_BASE_DIR);
        messages.add("  - 目录存在: " + (baseDir.exists() ? "✅" : "❌"));
        messages.add("  - 可读权限: " + (baseDir.canRead() ? "✅" : "❌"));
        messages.add("  - 可写权限: " + (baseDir.canWrite() ? "✅" : "❌"));

        if (!baseDir.exists()) {
            suggestions.add("创建基础目录: sudo mkdir -p " + WEATHER_BASE_DIR);
            suggestions.add("设置目录权限: sudo chown -R $USER:$USER /data/upload");
            suggestions.add("设置目录权限: sudo chmod -R 755 /data/upload");
        }

        // 4. 检查子目录
        String[] subDirs = {"micaps", "observation", "data", "precipitation-area"};
        messages.add("📂 子目录检查:");
        for (String subDir : subDirs) {
            File dir = new File(WEATHER_BASE_DIR + subDir);
            messages.add("  - " + subDir + ": " + (dir.exists() ? "✅" : "❌"));
            if (!dir.exists()) {
                suggestions.add("创建子目录: sudo mkdir -p " + WEATHER_BASE_DIR + subDir);
            }
        }

        // 5. 路径解析测试
        messages.add("🔍 路径解析测试:");
        List<String> testPaths = generateTestPaths(filePath);
        boolean fileFound = false;
        String foundPath = null;

        for (String testPath : testPaths) {
            File testFile = new File(testPath);
            boolean exists = testFile.exists();
            messages.add("  - 测试路径: " + testPath + " -> " + (exists ? "✅ 找到" : "❌ 未找到"));
            
            if (exists && !fileFound) {
                fileFound = true;
                foundPath = testPath;
            }
        }

        // 6. 生成建议
        if (!fileFound) {
            suggestions.add("文件未找到，请检查:");
            suggestions.add("1. 文件是否已正确上传");
            suggestions.add("2. 文件路径是否正确");
            suggestions.add("3. 目录权限是否正确");
            suggestions.add("4. 运行 ubuntu-setup.sh 脚本设置目录");
        } else {
            messages.add("✅ 文件找到: " + foundPath);
        }

        result.put("success", fileFound);
        result.put("foundPath", foundPath);
        result.put("messages", messages);
        result.put("suggestions", suggestions);
        result.put("testPaths", testPaths);

        return result;
    }

    /**
     * 生成测试路径列表
     */
    private static List<String> generateTestPaths(String filePath) {
        List<String> testPaths = new ArrayList<>();

        // 1. 原始路径
        testPaths.add(filePath);

        // 2. 如果是相对路径，尝试不同的基础路径
        if (!filePath.startsWith("/")) {
            // 天气模块路径
            if (filePath.startsWith("weather/")) {
                testPaths.add(WEATHER_BASE_DIR + filePath.substring("weather/".length()));
            } else {
                testPaths.add(WEATHER_BASE_DIR + filePath);
            }

            // 工作目录路径
            testPaths.add(System.getProperty("user.dir") + "/" + filePath);

            // 通用上传目录
            testPaths.add("/data/upload/" + filePath);
        }

        return testPaths;
    }

    /**
     * 检查目录结构
     */
    public static Map<String, Object> checkDirectoryStructure() {
        Map<String, Object> result = new HashMap<>();
        List<String> messages = new ArrayList<>();

        messages.add("📁 检查目录结构:");

        try {
            Path basePath = Paths.get(WEATHER_BASE_DIR);
            if (Files.exists(basePath)) {
                messages.add("✅ 基础目录存在: " + WEATHER_BASE_DIR);
                
                // 检查子目录
                String[] subDirs = {"micaps", "observation", "data", "precipitation-area"};
                for (String subDir : subDirs) {
                    Path subPath = basePath.resolve(subDir);
                    if (Files.exists(subPath)) {
                        messages.add("✅ 子目录存在: " + subDir);
                        
                        // 列出文件
                        try {
                            long fileCount = Files.list(subPath).count();
                            messages.add("  - 文件数量: " + fileCount);
                        } catch (Exception e) {
                            messages.add("  - 无法读取文件列表: " + e.getMessage());
                        }
                    } else {
                        messages.add("❌ 子目录不存在: " + subDir);
                    }
                }
                result.put("success", true);
            } else {
                messages.add("❌ 基础目录不存在: " + WEATHER_BASE_DIR);
                result.put("success", false);
            }
        } catch (Exception e) {
            messages.add("❌ 检查目录时出错: " + e.getMessage());
            result.put("success", false);
        }

        result.put("messages", messages);
        return result;
    }
}
