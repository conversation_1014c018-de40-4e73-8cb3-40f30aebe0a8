package com.yf.exam.modules.weather.scoring.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 站点评分详细信息
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
public class StationScoringDetail {
    
    /** 站点ID */
    private Long stationId;
    
    /** 站点名称 */
    private String stationName;
    
    /** 经度 */
    private Double longitude;
    
    /** 纬度 */
    private Double latitude;
    
    /** 实况降水量（毫米） */
    private Double actualPrecipitation;
    
    /** 实况降水等级 */
    private String actualLevel;
    
    /** 考生预报降水量（毫米） */
    private Double studentForecastPrecipitation;
    
    /** 考生预报降水等级 */
    private String studentForecastLevel;
    
    /** CMA-MESO预报降水量（毫米） */
    private Double cmaMesoForecastPrecipitation;
    
    /** CMA-MESO预报降水等级 */
    private String cmaMesoForecastLevel;
    
    /** 晴雨评分详情 */
    private RainNoRainScoringDetail rainNoRainDetail;
    
    /** 各量级评分详情 */
    private LevelScoringDetail levelDetail;
    
    /** 站点位置描述 */
    public String getLocationDescription() {
        return String.format("%.2f°E, %.2f°N", longitude, latitude);
    }
    
    /** 实况描述 */
    public String getActualDescription() {
        return String.format("%.1fmm(%s)", actualPrecipitation, actualLevel);
    }
    
    /** 考生预报描述 */
    public String getStudentForecastDescription() {
        return String.format("%.1fmm(%s)", studentForecastPrecipitation, studentForecastLevel);
    }
    
    /** CMA-MESO预报描述 */
    public String getCmaMesoForecastDescription() {
        return String.format("%.1fmm(%s)", cmaMesoForecastPrecipitation, cmaMesoForecastLevel);
    }
    
    /**
     * 晴雨评分详情
     */
    @Data
    public static class RainNoRainScoringDetail {
        /** 实况是否有雨 */
        private Boolean actualHasRain;
        
        /** 考生预报是否有雨 */
        private Boolean studentForecastHasRain;
        
        /** CMA-MESO预报是否有雨 */
        private Boolean cmaMesoForecastHasRain;
        
        /** 考生预报结果类型（正确A/空报B/漏报C/正确D） */
        private String studentResultType;
        
        /** CMA-MESO预报结果类型 */
        private String cmaMesoResultType;
        
        /** 考生对晴雨TS的贡献 */
        private String studentContribution;
        
        /** CMA-MESO对晴雨TS的贡献 */
        private String cmaMesoContribution;
    }
    
    /**
     * 量级评分详情
     */
    @Data
    public static class LevelScoringDetail {
        /** 评分的降水量级 */
        private String level;
        
        /** 是否参与该量级评分 */
        private Boolean participateInScoring;
        
        /** 考生预报结果类型（正确A/错误B/漏报C） */
        private String studentResultType;
        
        /** CMA-MESO预报结果类型 */
        private String cmaMesoResultType;
        
        /** 考生对该量级TS的贡献 */
        private String studentContribution;
        
        /** CMA-MESO对该量级TS的贡献 */
        private String cmaMesoContribution;
        
        /** 特殊规则说明（如微量降水特殊规则） */
        private String specialRuleNote;
    }
    
    @Override
    public String toString() {
        return String.format("StationScoringDetail{stationId=%d, location=(%s), " +
                           "actual=%s, student=%s, cmaMeso=%s}", 
                           stationId, getLocationDescription(),
                           getActualDescription(), 
                           getStudentForecastDescription(),
                           getCmaMesoForecastDescription());
    }
}
