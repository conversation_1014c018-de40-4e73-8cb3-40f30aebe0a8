package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.util.WeatherFilePathUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.*;

/**
 * 快速调试控制器
 * 专门用于排查文件找不到的问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/quick-debug")
@Api(tags = "快速调试")
public class QuickDebugController extends BaseController {

    private static final String BASE_DIR = "/data/upload/weather/";

    /**
     * 快速检查文件是否存在
     */
    @ApiOperation(value = "快速检查文件")
    @PostMapping("/check-file")
    public ApiRest<Map<String, Object>> checkFile(@RequestParam("filePath") String filePath) {
        Map<String, Object> result = new HashMap<>();
        
        log.info("快速检查文件: {}", filePath);
        
        // 1. 检查原始路径
        File originalFile = new File(filePath);
        result.put("originalPath", filePath);
        result.put("originalExists", originalFile.exists());
        
        // 2. 如果是相对路径，尝试拼接基础路径
        if (!filePath.startsWith("/")) {
            String absolutePath;
            if (filePath.startsWith("weather/")) {
                absolutePath = BASE_DIR + filePath.substring("weather/".length());
            } else {
                absolutePath = BASE_DIR + filePath;
            }
            
            File absoluteFile = new File(absolutePath);
            result.put("absolutePath", absolutePath);
            result.put("absoluteExists", absoluteFile.exists());
            
            if (absoluteFile.exists()) {
                result.put("fileSize", absoluteFile.length());
                result.put("canRead", absoluteFile.canRead());
                result.put("lastModified", new Date(absoluteFile.lastModified()));
            }
        }
        
        // 3. 使用工具类查找
        String foundPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
        result.put("toolFoundPath", foundPath);
        result.put("toolFoundExists", foundPath != null);
        
        // 4. 总结
        boolean anyExists = originalFile.exists() || 
                           (result.containsKey("absoluteExists") && (Boolean)result.get("absoluteExists")) ||
                           foundPath != null;
        result.put("success", anyExists);
        
        return super.success(result);
    }

    /**
     * 检查目录状态
     */
    @ApiOperation(value = "检查目录状态")
    @GetMapping("/check-dirs")
    public ApiRest<Map<String, Object>> checkDirectories() {
        Map<String, Object> result = new HashMap<>();
        
        // 检查基础目录
        File baseDir = new File(BASE_DIR);
        Map<String, Object> baseDirInfo = new HashMap<>();
        baseDirInfo.put("path", BASE_DIR);
        baseDirInfo.put("exists", baseDir.exists());
        baseDirInfo.put("canRead", baseDir.canRead());
        baseDirInfo.put("canWrite", baseDir.canWrite());
        result.put("baseDirectory", baseDirInfo);
        
        // 检查子目录
        String[] subDirs = {"micaps", "observation", "data", "precipitation-area"};
        Map<String, Object> subDirInfo = new HashMap<>();
        
        for (String subDir : subDirs) {
            File dir = new File(BASE_DIR + subDir);
            Map<String, Object> info = new HashMap<>();
            info.put("exists", dir.exists());
            info.put("canRead", dir.canRead());
            info.put("canWrite", dir.canWrite());
            
            if (dir.exists()) {
                File[] files = dir.listFiles();
                info.put("fileCount", files != null ? files.length : 0);
                
                // 列出前5个文件
                if (files != null && files.length > 0) {
                    List<String> fileNames = new ArrayList<>();
                    for (int i = 0; i < Math.min(5, files.length); i++) {
                        fileNames.add(files[i].getName());
                    }
                    info.put("sampleFiles", fileNames);
                }
            }
            
            subDirInfo.put(subDir, info);
        }
        
        result.put("subDirectories", subDirInfo);
        result.put("success", baseDir.exists());
        
        return super.success(result);
    }

    /**
     * 批量检查文件路径
     */
    @ApiOperation(value = "批量检查文件")
    @PostMapping("/batch-check")
    public ApiRest<Map<String, Object>> batchCheck(@RequestBody List<String> filePaths) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> results = new ArrayList<>();
        
        for (String filePath : filePaths) {
            Map<String, Object> fileResult = new HashMap<>();
            fileResult.put("filePath", filePath);
            
            // 使用工具类查找
            String foundPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
            fileResult.put("found", foundPath != null);
            fileResult.put("foundPath", foundPath);
            
            if (foundPath != null) {
                File file = new File(foundPath);
                fileResult.put("size", file.length());
                fileResult.put("canRead", file.canRead());
            }
            
            results.add(fileResult);
        }
        
        result.put("results", results);
        result.put("success", true);
        
        return super.success(result);
    }

    /**
     * 获取系统基本信息
     */
    @ApiOperation(value = "获取系统信息")
    @GetMapping("/system-info")
    public ApiRest<Map<String, Object>> getSystemInfo() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("osName", System.getProperty("os.name"));
        result.put("osVersion", System.getProperty("os.version"));
        result.put("userName", System.getProperty("user.name"));
        result.put("userDir", System.getProperty("user.dir"));
        result.put("javaVersion", System.getProperty("java.version"));
        result.put("fileSeparator", System.getProperty("file.separator"));
        
        // 检查基础目录
        File baseDir = new File(BASE_DIR);
        result.put("baseDirExists", baseDir.exists());
        result.put("baseDirCanRead", baseDir.canRead());
        result.put("baseDirCanWrite", baseDir.canWrite());
        
        result.put("success", true);
        
        return super.success(result);
    }

    /**
     * 创建测试文件
     */
    @ApiOperation(value = "创建测试文件")
    @PostMapping("/create-test-file")
    public ApiRest<Map<String, Object>> createTestFile(@RequestParam("subDir") String subDir) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testDir = BASE_DIR + subDir + "/";
            File dir = new File(testDir);
            
            // 创建目录（如果不存在）
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                result.put("dirCreated", created);
            }
            
            // 创建测试文件
            String testFilePath = testDir + "test_" + System.currentTimeMillis() + ".txt";
            File testFile = new File(testFilePath);
            
            boolean fileCreated = testFile.createNewFile();
            result.put("fileCreated", fileCreated);
            result.put("testFilePath", testFilePath);
            
            if (fileCreated) {
                // 写入测试内容
                java.nio.file.Files.write(testFile.toPath(), "Test content".getBytes());
                result.put("contentWritten", true);
                result.put("fileSize", testFile.length());
            }
            
            result.put("success", fileCreated);
            
        } catch (Exception e) {
            log.error("创建测试文件失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return super.success(result);
    }
}
