package com.yf.exam.modules.weather.scoring.example;

import com.yf.exam.modules.weather.scoring.service.PrecipitationAreaScoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 多边形内点判断算法使用示例
 * 演示如何配置和使用不同的算法来处理手绘多边形
 */
@Slf4j
@Component
public class PolygonAlgorithmExample {

    @Autowired
    private PrecipitationAreaScoringService precipitationService;

    /**
     * 演示基本使用方法
     */
    public void demonstrateBasicUsage() {
        log.info("=== 多边形内点判断算法演示 ===");

        // 1. 使用默认配置（推荐）
        log.info("1. 使用默认配置（多方法综合算法）");
        testWithDefaultSettings();

        // 2. 使用不同算法
        log.info("2. 测试不同算法");
        testDifferentAlgorithms();

        // 3. 自定义容差配置
        log.info("3. 测试不同容差配置");
        testDifferentTolerances();

        // 4. 处理手绘多边形问题
        log.info("4. 处理手绘多边形问题");
        testHandDrawnPolygons();
    }

    /**
     * 使用默认配置进行测试
     */
    private void testWithDefaultSettings() {
        // 创建考生答案数据（模拟手绘的不完美多边形）
        Map<String, Object> studentAnswer = createStudentAnswerWithImperfectPolygon();

        try {
            // 使用默认配置进行评分
            // precipitationService.calculatePrecipitationScore(...);
            log.info("默认配置测试完成 - 使用多方法综合算法，容差: 距离2km, 闭合1km");
        } catch (Exception e) {
            log.error("默认配置测试失败: {}", e.getMessage());
        }
    }

    /**
     * 测试不同算法
     */
    private void testDifferentAlgorithms() {
        String[] algorithms = {"ray_casting", "winding_number", "multi_method"};
        String[] algorithmNames = {"射线法", "卷积角度法", "多方法综合"};

        for (int i = 0; i < algorithms.length; i++) {
            log.info("测试算法: {} ({})", algorithmNames[i], algorithms[i]);
            
            // 设置算法
            precipitationService.setPointInPolygonAlgorithm(algorithms[i]);
            
            // 这里可以进行实际的评分测试
            // 由于需要实际的文件路径和数据，这里只做配置演示
            log.info("算法 {} 配置完成", algorithmNames[i]);
        }
    }

    /**
     * 测试不同容差配置
     */
    private void testDifferentTolerances() {
        // 测试不同的容差配置
        double[][] tolerances = {
            {0.01, 0.005},  // 严格模式：距离1km，闭合0.5km
            {0.02, 0.01},   // 标准模式：距离2km，闭合1km（默认）
            {0.05, 0.02}    // 宽松模式：距离5km，闭合2km
        };
        
        String[] modes = {"严格模式", "标准模式", "宽松模式"};

        for (int i = 0; i < tolerances.length; i++) {
            log.info("测试容差配置: {} - 距离容差: {}度, 闭合容差: {}度", 
                    modes[i], tolerances[i][0], tolerances[i][1]);
            
            precipitationService.setPolygonTolerances(tolerances[i][0], tolerances[i][1]);
            
            // 这里可以进行实际测试
            log.info("容差配置 {} 设置完成", modes[i]);
        }
    }

    /**
     * 演示处理手绘多边形的常见问题
     */
    private void testHandDrawnPolygons() {
        log.info("演示手绘多边形常见问题的处理");

        // 问题1：不完美闭合
        log.info("问题1: 处理不完美闭合的多边形");
        Map<String, Object> imperfectAnswer = createImperfectClosureExample();
        log.info("创建了包含小尾巴的多边形数据");

        // 问题2：复杂不规则形状
        log.info("问题2: 处理复杂不规则形状");
        Map<String, Object> complexAnswer = createComplexShapeExample();
        log.info("创建了复杂不规则形状的多边形数据");

        // 问题3：边界附近的点
        log.info("问题3: 处理边界附近的点");
        demonstrateBoundaryPointHandling();

        // 推荐配置
        log.info("推荐配置: 多方法综合 + 适中容差");
        precipitationService.setPointInPolygonAlgorithm("multi_method");
        precipitationService.setPolygonTolerances(0.02, 0.01);
    }

    /**
     * 创建包含不完美多边形的考生答案
     */
    private Map<String, Object> createStudentAnswerWithImperfectPolygon() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> areas = new HashMap<>();

        // 创建一个不完美闭合的小雨区域
        List<Map<String, Object>> lightRainAreas = new ArrayList<>();
        Map<String, Object> area = new HashMap<>();
        Map<String, Object> geometry = new HashMap<>();

        // 不完美闭合的坐标（最后一点与第一点有小偏差）
        List<List<Double>> coordinates = Arrays.asList(
            Arrays.asList(116.0, 39.0),
            Arrays.asList(118.0, 39.0),
            Arrays.asList(118.0, 41.0),
            Arrays.asList(116.0, 41.0),
            Arrays.asList(116.005, 39.003)  // 小尾巴：距离第一点约0.5km
        );

        geometry.put("type", "Polygon");
        geometry.put("coordinates", Arrays.asList(coordinates));
        area.put("geometry", geometry);
        lightRainAreas.add(area);

        areas.put("level0", lightRainAreas);  // level0 对应小雨
        content.put("areas", areas);
        answer.put("content", content);

        return answer;
    }

    /**
     * 创建不完美闭合的示例
     */
    private Map<String, Object> createImperfectClosureExample() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> areas = new HashMap<>();

        List<Map<String, Object>> areas_list = new ArrayList<>();
        Map<String, Object> area = new HashMap<>();
        Map<String, Object> geometry = new HashMap<>();

        // 模拟手绘时的不完美闭合
        List<List<Double>> coordinates = Arrays.asList(
            Arrays.asList(116.0, 39.0),
            Arrays.asList(117.5, 39.2),
            Arrays.asList(118.0, 40.0),
            Arrays.asList(117.8, 41.0),
            Arrays.asList(116.5, 41.2),
            Arrays.asList(115.8, 40.5),
            Arrays.asList(116.008, 39.006)  // 手绘尾巴
        );

        geometry.put("type", "Polygon");
        geometry.put("coordinates", Arrays.asList(coordinates));
        area.put("geometry", geometry);
        areas_list.add(area);

        areas.put("level10", areas_list);  // level10 对应暴雨
        content.put("areas", areas);
        answer.put("content", content);

        return answer;
    }

    /**
     * 创建复杂形状的示例
     */
    private Map<String, Object> createComplexShapeExample() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> areas = new HashMap<>();

        List<Map<String, Object>> areas_list = new ArrayList<>();
        Map<String, Object> area = new HashMap<>();
        Map<String, Object> geometry = new HashMap<>();

        // 创建一个复杂的凹多边形
        List<List<Double>> coordinates = Arrays.asList(
            Arrays.asList(116.0, 39.0),
            Arrays.asList(117.0, 39.0),
            Arrays.asList(117.0, 39.5),
            Arrays.asList(116.5, 39.5),  // 凹进去
            Arrays.asList(116.5, 40.5),
            Arrays.asList(117.0, 40.5),
            Arrays.asList(117.0, 41.0),
            Arrays.asList(116.0, 41.0),
            Arrays.asList(116.0, 39.0)
        );

        geometry.put("type", "Polygon");
        geometry.put("coordinates", Arrays.asList(coordinates));
        area.put("geometry", geometry);
        areas_list.add(area);

        areas.put("level5", areas_list);  // level5 对应大雨
        content.put("areas", areas);
        answer.put("content", content);

        return answer;
    }

    /**
     * 演示边界点处理
     */
    private void demonstrateBoundaryPointHandling() {
        log.info("边界点处理演示:");
        log.info("- 当点非常接近多边形边界时，多方法综合算法会倾向于判断为内部");
        log.info("- 这种处理方式对考生更友好，避免因绘制精度问题导致的误判");
        log.info("- 可以通过调整距离容差来控制这种行为");
        
        // 演示不同容差下的行为
        double[] testTolerances = {0.01, 0.02, 0.05};
        for (double tolerance : testTolerances) {
            log.info("距离容差 {}度 (约{}km) 的边界处理效果", 
                    tolerance, Math.round(tolerance * 111));
        }
    }

    /**
     * 性能测试示例
     */
    public void performanceTest() {
        log.info("=== 性能测试 ===");
        
        String[] algorithms = {"ray_casting", "winding_number", "multi_method"};
        String[] names = {"射线法", "卷积角度法", "多方法综合"};
        
        for (int i = 0; i < algorithms.length; i++) {
            precipitationService.setPointInPolygonAlgorithm(algorithms[i]);
            
            long startTime = System.currentTimeMillis();
            
            // 模拟大量点的判断（实际使用中会通过评分服务调用）
            // 这里只是演示性能测试的思路
            for (int j = 0; j < 1000; j++) {
                // 模拟点在多边形判断的调用
                // 实际测试需要真实的多边形数据
            }
            
            long endTime = System.currentTimeMillis();
            log.info("{} 算法性能测试完成，耗时: {}ms", names[i], endTime - startTime);
        }
    }

    /**
     * 最佳实践建议
     */
    public void bestPractices() {
        log.info("=== 最佳实践建议 ===");
        
        log.info("1. 默认配置适合大多数场景:");
        log.info("   - 算法: multi_method (多方法综合)");
        log.info("   - 距离容差: 0.02度 (约2km)");
        log.info("   - 闭合容差: 0.01度 (约1km)");
        
        log.info("2. 高性能场景 (大量站点):");
        log.info("   - 算法: ray_casting (射线法)");
        log.info("   - 适当减小容差以提高速度");
        
        log.info("3. 手绘友好场景:");
        log.info("   - 算法: multi_method (多方法综合)");
        log.info("   - 增大容差: 距离0.05度, 闭合0.02度");
        
        log.info("4. 高精度场景:");
        log.info("   - 算法: winding_number (卷积角度法)");
        log.info("   - 减小容差: 距离0.01度, 闭合0.005度");
    }
}
