package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.config.WeatherUploadConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 简化版配置测试控制器
 * 兼容Java 8
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/simple-config-test")
@Api(tags = "简化配置测试")
public class SimpleConfigTestController extends BaseController {

    @Autowired
    private WeatherUploadConfig weatherUploadConfig;

    /**
     * 快速检查配置
     */
    @ApiOperation(value = "快速检查配置")
    @GetMapping("/quick-check")
    public ApiRest<Map<String, Object>> quickCheck() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查配置注入
            if (weatherUploadConfig == null) {
                result.put("success", false);
                result.put("message", "配置未注入");
                return super.success(result);
            }

            // 获取配置路径
            String baseDir = weatherUploadConfig.getBaseDir();
            result.put("baseDir", baseDir);
            result.put("baseDirExists", new File(baseDir).exists());

            // 检查子目录
            result.put("micapsDir", weatherUploadConfig.getMicapsDir());
            result.put("micapsDirExists", new File(weatherUploadConfig.getMicapsDir()).exists());
            
            result.put("observationDir", weatherUploadConfig.getObservationDir());
            result.put("observationDirExists", new File(weatherUploadConfig.getObservationDir()).exists());

            result.put("success", true);
            result.put("message", "配置检查完成");

            return super.success(result);

        } catch (Exception e) {
            log.error("快速检查配置时出错", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
            return super.success(result);
        }
    }

    /**
     * 生成访问URL示例
     */
    @ApiOperation(value = "生成访问URL示例")
    @GetMapping("/url-example")
    public ApiRest<Map<String, Object>> urlExample(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String contextPath = request.getContextPath();

            StringBuilder baseUrl = new StringBuilder();
            baseUrl.append(scheme).append("://").append(serverName);

            if ((scheme.equals("http") && serverPort != 80) || 
                (scheme.equals("https") && serverPort != 443)) {
                baseUrl.append(":").append(serverPort);
            }

            baseUrl.append(contextPath);
            if (!contextPath.endsWith("/")) {
                baseUrl.append("/");
            }
            baseUrl.append("upload/file/weather/");

            result.put("baseUrl", baseUrl.toString());
            result.put("micapsUrl", baseUrl.toString() + "micaps/example.dat");
            result.put("observationUrl", baseUrl.toString() + "observation/example.dat");
            result.put("success", true);

            return super.success(result);

        } catch (Exception e) {
            log.error("生成URL示例时出错", e);
            result.put("success", false);
            result.put("message", "生成失败: " + e.getMessage());
            return super.success(result);
        }
    }

    /**
     * 创建必要目录
     */
    @ApiOperation(value = "创建必要目录")
    @PostMapping("/create-dirs")
    public ApiRest<Map<String, Object>> createDirs() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (weatherUploadConfig == null) {
                result.put("success", false);
                result.put("message", "配置未注入");
                return super.success(result);
            }

            // 创建基础目录
            File baseDir = new File(weatherUploadConfig.getBaseDir());
            if (!baseDir.exists()) {
                boolean created = baseDir.mkdirs();
                result.put("baseDirCreated", created);
            } else {
                result.put("baseDirCreated", false);
                result.put("baseDirExisted", true);
            }

            // 创建子目录
            File micapsDir = new File(weatherUploadConfig.getMicapsDir());
            if (!micapsDir.exists()) {
                boolean created = micapsDir.mkdirs();
                result.put("micapsDirCreated", created);
            }

            File observationDir = new File(weatherUploadConfig.getObservationDir());
            if (!observationDir.exists()) {
                boolean created = observationDir.mkdirs();
                result.put("observationDirCreated", created);
            }

            File dataDir = new File(weatherUploadConfig.getDataDir());
            if (!dataDir.exists()) {
                boolean created = dataDir.mkdirs();
                result.put("dataDirCreated", created);
            }

            File precipitationDir = new File(weatherUploadConfig.getPrecipitationAreaDir());
            if (!precipitationDir.exists()) {
                boolean created = precipitationDir.mkdirs();
                result.put("precipitationDirCreated", created);
            }

            result.put("success", true);
            result.put("message", "目录创建完成");

            return super.success(result);

        } catch (Exception e) {
            log.error("创建目录时出错", e);
            result.put("success", false);
            result.put("message", "创建失败: " + e.getMessage());
            return super.success(result);
        }
    }

    /**
     * 检查目录权限
     */
    @ApiOperation(value = "检查目录权限")
    @GetMapping("/check-permissions")
    public ApiRest<Map<String, Object>> checkPermissions() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (weatherUploadConfig == null) {
                result.put("success", false);
                result.put("message", "配置未注入");
                return super.success(result);
            }

            File baseDir = new File(weatherUploadConfig.getBaseDir());
            result.put("baseDirExists", baseDir.exists());
            result.put("baseDirCanRead", baseDir.canRead());
            result.put("baseDirCanWrite", baseDir.canWrite());

            File micapsDir = new File(weatherUploadConfig.getMicapsDir());
            result.put("micapsDirExists", micapsDir.exists());
            result.put("micapsDirCanRead", micapsDir.canRead());
            result.put("micapsDirCanWrite", micapsDir.canWrite());

            File observationDir = new File(weatherUploadConfig.getObservationDir());
            result.put("observationDirExists", observationDir.exists());
            result.put("observationDirCanRead", observationDir.canRead());
            result.put("observationDirCanWrite", observationDir.canWrite());

            result.put("success", true);
            result.put("message", "权限检查完成");

            return super.success(result);

        } catch (Exception e) {
            log.error("检查权限时出错", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
            return super.success(result);
        }
    }
}
