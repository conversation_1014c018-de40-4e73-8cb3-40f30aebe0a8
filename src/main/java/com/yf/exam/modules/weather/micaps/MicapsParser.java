package com.yf.exam.modules.weather.micaps;

import com.yf.exam.modules.weather.util.WeatherFilePathUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * MICAPS数据解析器
 * 支持解析第一类（地面全要素填图数据）和第四类（格点数据）MICAPS文件
 *
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Slf4j
public class MicapsParser {

    /**
     * 解析MICAPS文件
     *
     * @param filePath 文件路径
     * @return 解析结果对象
     * @throws IOException 文件读取异常
     * @throws IllegalArgumentException 不支持的数据类型异常
     */
    public static MicapsData parseMicapsFile(String filePath) throws IOException {
        // 使用路径解析工具来查找文件
        String resolvedPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
        if (resolvedPath == null) {
            throw new FileNotFoundException("MICAPS文件不存在: " + filePath);
        }

        log.debug("解析MICAPS文件：{} -> {}", filePath, resolvedPath);
        File file = new File(resolvedPath);

        // 尝试以文本方式读取文件头
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {

            String firstLine = reader.readLine();
            if (firstLine == null || !firstLine.trim().startsWith("diamond")) {
                throw new IllegalArgumentException("不是有效的MICAPS文件格式");
            }

            // 解析文件头确定数据类型
            String[] headerParts = firstLine.trim().split("\\s+");
            if (headerParts.length < 2) {
                throw new IllegalArgumentException("MICAPS文件头格式错误");
            }

            int dataType;
            try {
                dataType = Integer.parseInt(headerParts[1]);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("无法解析MICAPS数据类型");
            }

            // 根据数据类型调用相应的解析方法
            switch (dataType) {
                case 1:
                    return parseType1Data(file);
                case 4:
                    return parseType4Data(file);
                default:
                    throw new IllegalArgumentException("不支持的MICAPS数据类型: " + dataType +
                        "，当前仅支持第一类和第四类数据");
            }
        }
    }

    /**
     * 解析第一类数据：地面全要素填图数据
     *
     * @param file MICAPS文件
     * @return 第一类数据对象
     * @throws IOException 文件读取异常
     */
    private static MicapsType1Data parseType1Data(File file) throws IOException {
        try (Scanner scanner = new Scanner(file, StandardCharsets.UTF_8)) {

            // 解析文件头
            String headerLine = scanner.nextLine().trim();
            String[] headerParts = headerLine.split("\\s+", 7); // 最多分割7部分

            if (headerParts.length < 7) {
                throw new IllegalArgumentException("第一类数据文件头格式错误");
            }

            MicapsType1Data data = new MicapsType1Data();
            data.setDataType(1);
            data.setDescription(headerParts[2]); // 数据说明

            // 解析时间信息
            try {
                data.setYear(Integer.parseInt(headerParts[3]));
                data.setMonth(Integer.parseInt(headerParts[4]));
                data.setDay(Integer.parseInt(headerParts[5]));
                data.setHour(Integer.parseInt(headerParts[6]));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("第一类数据时间信息解析错误: " + e.getMessage());
            }

            // 解析总站点数
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("第一类数据缺少站点数信息");
            }

            String stationCountLine = scanner.nextLine().trim();
            int totalStations;
            try {
                totalStations = Integer.parseInt(stationCountLine);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("第一类数据站点数解析错误: " + e.getMessage());
            }

            data.setTotalStations(totalStations);

            // 解析站点数据
            List<MicapsStation> stations = new ArrayList<>();
            int stationCount = 0;

            while (scanner.hasNextLine() && stationCount < totalStations) {
                String stationLine = scanner.nextLine().trim();
                if (stationLine.isEmpty()) {
                    continue;
                }

                try {
                    MicapsStation station = parseStationData(stationLine);
                    stations.add(station);
                    stationCount++;
                } catch (Exception e) {
                    System.err.println("解析站点数据时出错，跳过该行: " + stationLine +
                        ", 错误: " + e.getMessage());
                }
            }

            data.setStations(stations);

            return data;
        }
    }

    /**
     * 解析站点数据行
     *
     * @param stationLine 站点数据行
     * @return 站点对象
     */
    private static MicapsStation parseStationData(String stationLine) {
        String[] parts = stationLine.split("\\s+");

        if (parts.length < 20) {
            throw new IllegalArgumentException("站点数据字段不足，至少需要20个字段");
        }

        MicapsStation station = new MicapsStation();

        try {
            // 基本信息
            station.setStationId(Long.parseLong(parts[0]));
            station.setLongitude(Double.parseDouble(parts[1]));
            station.setLatitude(Double.parseDouble(parts[2]));
            station.setElevation(Double.parseDouble(parts[3]));
            station.setLevel(Integer.parseInt(parts[4]));

            // 气象要素（使用9999表示缺值）
            station.setTotalCloudCover(parseIntValue(parts[5]));
            station.setWindDirection(parseIntValue(parts[6]));
            station.setWindSpeed(parseIntValue(parts[7]));
            station.setPressure(parseDoubleValue(parts[8]));
            station.setPressureChange3h(parseDoubleValue(parts[9]));
            station.setPastWeather1(parseIntValue(parts[10]));
            station.setPastWeather2(parseIntValue(parts[11]));
            station.setPrecipitation6h(parseDoubleValue(parts[12]));
            station.setLowCloudType(parseIntValue(parts[13]));
            station.setLowCloudAmount(parseIntValue(parts[14]));
            station.setLowCloudHeight(parseDoubleValue(parts[15]));
            station.setDewPoint(parseDoubleValue(parts[16]));
            station.setVisibility(parseDoubleValue(parts[17]));
            station.setPresentWeather(parseIntValue(parts[18]));
            station.setTemperature(parseDoubleValue(parts[19]));

            // 可选字段
            if (parts.length > 20) {
                station.setMiddleCloudType(parseIntValue(parts[20]));
            }
            if (parts.length > 21) {
                station.setHighCloudType(parseIntValue(parts[21]));
            }
            if (parts.length > 22) {
                station.setFlag1(parseIntValue(parts[22]));
            }
            if (parts.length > 23) {
                station.setFlag2(parseIntValue(parts[23]));
            }
            if (parts.length > 24) {
                station.setTemperatureChange24h(parseDoubleValue(parts[24]));
            }
            if (parts.length > 25) {
                station.setPressureChange24h(parseDoubleValue(parts[25]));
            }

        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("站点数据数值解析错误: " + e.getMessage());
        }

        return station;
    }

    /**
     * 解析整数值，9999表示缺值
     */
    private static Integer parseIntValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            return intValue == 9999 ? null : intValue;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 解析浮点数值，9999表示缺值
     */
    private static Double parseDoubleValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            return doubleValue == 9999.0 ? null : doubleValue;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 解析第四类数据：格点数据
     *
     * @param file MICAPS文件
     * @return 第四类数据对象
     * @throws IOException 文件读取异常
     */
    private static MicapsType4Data parseType4Data(File file) throws IOException {
        try (Scanner scanner = new Scanner(file, StandardCharsets.UTF_8)) {

            // 解析文件头第一行
            String headerLine1 = scanner.nextLine().trim();
            String[] headerParts1 = headerLine1.split("\\s+", 8); // 最多分割8部分

            if (headerParts1.length < 8) {
                throw new IllegalArgumentException("第四类数据文件头第一行格式错误");
            }

            MicapsType4Data data = new MicapsType4Data();
            data.setDataType(4);
            data.setDescription(headerParts1[2]); // 数据说明

            // 解析时间信息
            try {
                data.setYear(Integer.parseInt(headerParts1[3]));
                data.setMonth(Integer.parseInt(headerParts1[4]));
                data.setDay(Integer.parseInt(headerParts1[5]));
                data.setHour(Integer.parseInt(headerParts1[6]));
                data.setForecastHour(Integer.parseInt(headerParts1[7]));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("第四类数据时间信息解析错误: " + e.getMessage());
            }

            // 解析文件头第二行
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("第四类数据缺少网格参数信息");
            }

            String headerLine2 = scanner.nextLine().trim();
            String[] headerParts2 = headerLine2.split("\\s+");

            if (headerParts2.length < 15) {
                throw new IllegalArgumentException("第四类数据文件头第二行格式错误，需要至少15个参数");
            }

            try {
                data.setLevel(Integer.parseInt(headerParts2[0]));
                data.setLonInterval(Double.parseDouble(headerParts2[1]));
                data.setLatInterval(Double.parseDouble(headerParts2[2]));
                data.setStartLon(Double.parseDouble(headerParts2[3]));
                data.setEndLon(Double.parseDouble(headerParts2[4]));
                data.setStartLat(Double.parseDouble(headerParts2[5]));
                data.setEndLat(Double.parseDouble(headerParts2[6]));
                data.setLatGridNum(Integer.parseInt(headerParts2[7]));
                data.setLonGridNum(Integer.parseInt(headerParts2[8]));
                data.setContourInterval(Double.parseDouble(headerParts2[9]));
                data.setContourStartValue(Double.parseDouble(headerParts2[10]));
                data.setContourEndValue(Double.parseDouble(headerParts2[11]));
                data.setSmoothFactor(Double.parseDouble(headerParts2[12]));
                data.setBoldLineValue(Double.parseDouble(headerParts2[13]));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("第四类数据网格参数解析错误: " + e.getMessage());
            }

            // 解析格点数据
            int totalGridPoints = data.getLatGridNum() * data.getLonGridNum();
            List<Double> gridValues = new ArrayList<>();

            while (scanner.hasNextLine() && gridValues.size() < totalGridPoints) {
                String dataLine = scanner.nextLine().trim();
                if (dataLine.isEmpty()) {
                    continue;
                }

                String[] values = dataLine.split("\\s+");
                for (String value : values) {
                    if (gridValues.size() >= totalGridPoints) {
                        break;
                    }

                    try {
                        double gridValue = Double.parseDouble(value);
                        gridValues.add(gridValue);
                    } catch (NumberFormatException e) {
                        System.err.println("跳过无效的格点数值: " + value);
                    }
                }
            }

            if (gridValues.size() != totalGridPoints) {
                System.err.println("警告: 实际读取的格点数(" + gridValues.size() +
                    ")与预期数量(" + totalGridPoints + ")不匹配");
            }

            data.setGridValues(gridValues);

            return data;
        }
    }
}
