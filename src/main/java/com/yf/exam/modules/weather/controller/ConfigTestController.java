package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.config.WeatherUploadConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置测试控制器
 * 用于测试配置是否正确加载
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/config-test")
@Api(tags = "配置测试")
public class ConfigTestController extends BaseController {

    @Autowired
    private WeatherUploadConfig weatherUploadConfig;

    /**
     * 测试配置加载
     */
    @ApiOperation(value = "测试配置加载")
    @GetMapping("/test-config")
    public ApiRest<Map<String, Object>> testConfig(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 检查配置是否注入
            if (weatherUploadConfig == null) {
                result.put("success", false);
                result.put("error", "WeatherUploadConfig未注入");
                return super.success(result);
            }

            // 2. 获取配置信息
            result.put("baseDir", weatherUploadConfig.getBaseDir());
            result.put("micapsDir", weatherUploadConfig.getMicapsDir());
            result.put("observationDir", weatherUploadConfig.getObservationDir());
            result.put("dataDir", weatherUploadConfig.getDataDir());
            result.put("precipitationAreaDir", weatherUploadConfig.getPrecipitationAreaDir());

            // 3. 检查目录是否存在
            Map<String, Object> dirStatus = new HashMap<>();
            dirStatus.put("baseDir", new File(weatherUploadConfig.getBaseDir()).exists());
            dirStatus.put("micapsDir", new File(weatherUploadConfig.getMicapsDir()).exists());
            dirStatus.put("observationDir", new File(weatherUploadConfig.getObservationDir()).exists());
            dirStatus.put("dataDir", new File(weatherUploadConfig.getDataDir()).exists());
            dirStatus.put("precipitationAreaDir", new File(weatherUploadConfig.getPrecipitationAreaDir()).exists());
            result.put("directoryExists", dirStatus);

            // 4. 生成示例访问URL
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String contextPath = request.getContextPath();
            
            StringBuilder baseUrl = new StringBuilder();
            baseUrl.append(scheme).append("://").append(serverName);
            
            if ((scheme.equals("http") && serverPort != 80) || 
                (scheme.equals("https") && serverPort != 443)) {
                baseUrl.append(":").append(serverPort);
            }
            
            baseUrl.append(contextPath);
            if (!contextPath.endsWith("/")) {
                baseUrl.append("/");
            }
            baseUrl.append("upload/file/weather/");
            
            result.put("generatedBaseUrl", baseUrl.toString());

            Map<String, String> sampleUrls = new HashMap<>();
            sampleUrls.put("micaps", baseUrl.toString() + "micaps/sample.dat");
            sampleUrls.put("observation", baseUrl.toString() + "observation/sample.dat");
            sampleUrls.put("data", baseUrl.toString() + "data/sample.zip");
            sampleUrls.put("precipitationArea", baseUrl.toString() + "precipitation-area/sample.txt");
            result.put("sampleUrls", sampleUrls);

            result.put("success", true);
            return super.success(result);

        } catch (Exception e) {
            log.error("测试配置时出错", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return super.success(result);
        }
    }

    /**
     * 创建测试目录
     */
    @ApiOperation(value = "创建测试目录")
    @PostMapping("/create-dirs")
    public ApiRest<Map<String, Object>> createDirectories() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (weatherUploadConfig == null) {
                result.put("success", false);
                result.put("error", "WeatherUploadConfig未注入");
                return super.success(result);
            }

            Map<String, Object> createResults = new HashMap<>();

            // 创建各个目录
            String[] dirs = {
                weatherUploadConfig.getBaseDir(),
                weatherUploadConfig.getMicapsDir(),
                weatherUploadConfig.getObservationDir(),
                weatherUploadConfig.getDataDir(),
                weatherUploadConfig.getPrecipitationAreaDir()
            };

            String[] dirNames = {"baseDir", "micapsDir", "observationDir", "dataDir", "precipitationAreaDir"};

            for (int i = 0; i < dirs.length; i++) {
                File dir = new File(dirs[i]);
                boolean existed = dir.exists();
                boolean created = false;
                
                if (!existed) {
                    created = dir.mkdirs();
                }
                
                Map<String, Object> dirResult = new HashMap<>();
                dirResult.put("path", dirs[i]);
                dirResult.put("existedBefore", existed);
                dirResult.put("created", created);
                dirResult.put("existsNow", dir.exists());
                dirResult.put("canRead", dir.canRead());
                dirResult.put("canWrite", dir.canWrite());
                
                createResults.put(dirNames[i], dirResult);
            }

            result.put("createResults", createResults);
            result.put("success", true);
            return super.success(result);

        } catch (Exception e) {
            log.error("创建目录时出错", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return super.success(result);
        }
    }

    /**
     * 测试URL生成
     */
    @ApiOperation(value = "测试URL生成")
    @GetMapping("/test-url-generation")
    public ApiRest<Map<String, Object>> testUrlGeneration(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取请求信息
            result.put("scheme", request.getScheme());
            result.put("serverName", request.getServerName());
            result.put("serverPort", request.getServerPort());
            result.put("contextPath", request.getContextPath());
            result.put("requestURL", request.getRequestURL().toString());
            result.put("requestURI", request.getRequestURI());

            // 生成各种URL示例
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String contextPath = request.getContextPath();
            
            StringBuilder baseUrl = new StringBuilder();
            baseUrl.append(scheme).append("://").append(serverName);
            
            if ((scheme.equals("http") && serverPort != 80) || 
                (scheme.equals("https") && serverPort != 443)) {
                baseUrl.append(":").append(serverPort);
            }
            
            baseUrl.append(contextPath);
            if (!contextPath.endsWith("/")) {
                baseUrl.append("/");
            }

            Map<String, String> urlExamples = new HashMap<>();
            urlExamples.put("baseUrl", baseUrl.toString());
            urlExamples.put("uploadFileBase", baseUrl.toString() + "upload/file/");
            urlExamples.put("weatherFileBase", baseUrl.toString() + "upload/file/weather/");
            urlExamples.put("micapsExample", baseUrl.toString() + "upload/file/weather/micaps/example.dat");
            urlExamples.put("observationExample", baseUrl.toString() + "upload/file/weather/observation/example.dat");

            result.put("urlExamples", urlExamples);
            result.put("success", true);
            return super.success(result);

        } catch (Exception e) {
            log.error("测试URL生成时出错", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return super.success(result);
        }
    }
}
