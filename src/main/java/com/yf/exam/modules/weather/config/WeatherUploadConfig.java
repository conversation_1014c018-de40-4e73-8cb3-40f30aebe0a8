package com.yf.exam.modules.weather.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 天气模块文件上传配置
 * 只配置存储路径，访问路径动态生成
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "conf.weather")
public class WeatherUploadConfig {

    /**
     * 天气文件存储基础路径，以/结束
     * 默认值：/data/upload/weather/
     */
    private String baseDir = "/data/upload/weather/";

    /**
     * 获取MICAPS文件存储目录
     */
    public String getMicapsDir() {
        return baseDir + "micaps/";
    }

    /**
     * 获取实况文件存储目录
     */
    public String getObservationDir() {
        return baseDir + "observation/";
    }

    /**
     * 获取数据文件存储目录
     */
    public String getDataDir() {
        return baseDir + "data/";
    }

    /**
     * 获取降水落区文件存储目录
     */
    public String getPrecipitationAreaDir() {
        return baseDir + "precipitation-area/";
    }

    /**
     * 确保基础路径以/结尾
     */
    public String getBaseDir() {
        if (baseDir != null && !baseDir.endsWith("/")) {
            return baseDir + "/";
        }
        return baseDir;
    }
}
