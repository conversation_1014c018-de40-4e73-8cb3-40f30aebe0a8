package com.yf.exam.modules.weather.service;

import com.yf.exam.ability.upload.config.UploadConfig;
import com.yf.exam.modules.weather.config.WeatherUploadConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;

/**
 * 天气模块文件路径解析服务
 *
 * 负责将相对路径转换为绝对路径，解决上传文件路径不一致的问题
 *
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
@Service
public class WeatherFilePathService {

    @Autowired
    private UploadConfig uploadConfig;

    @Autowired
    private WeatherUploadConfig weatherUploadConfig;

    /**
     * 解析文件路径，将相对路径转换为绝对路径
     * 
     * @param filePath 文件路径（可能是相对路径或绝对路径）
     * @return 绝对路径
     */
    public String resolveFilePath(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }

        // 如果已经是绝对路径，直接返回
        if (isAbsolutePath(filePath)) {
            return filePath;
        }

        // 处理相对路径
        String resolvedPath = resolveRelativePath(filePath);
        
        log.debug("文件路径解析：{} -> {}", filePath, resolvedPath);
        
        return resolvedPath;
    }

    /**
     * 判断是否为绝对路径
     */
    private boolean isAbsolutePath(String filePath) {
        // Windows路径：C:\ 或 D:\ 等（支持正斜杠和反斜杠）
        if (filePath.matches("^[A-Za-z]:[/\\\\].*")) {
            return true;
        }

        // Unix/Linux路径：以 / 开头
        if (filePath.startsWith("/")) {
            return true;
        }

        return false;
    }

    /**
     * 解析相对路径为绝对路径
     */
    private String resolveRelativePath(String relativePath) {
        // 处理天气模块的相对路径
        if (relativePath.startsWith("weather/")) {
            // 使用天气模块配置的基础路径
            String weatherBaseDir = getWeatherBaseDir();
            return weatherBaseDir + relativePath.substring("weather/".length());
        }

        // 处理其他相对路径，使用通用上传配置
        if (uploadConfig != null && StringUtils.hasText(uploadConfig.getDir())) {
            return uploadConfig.getDir() + relativePath;
        }

        // 默认使用天气模块配置路径
        return getWeatherBaseDir() + relativePath;
    }

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public boolean fileExists(String filePath) {
        String resolvedPath = resolveFilePath(filePath);
        if (resolvedPath == null) {
            return false;
        }
        
        File file = new File(resolvedPath);
        boolean exists = file.exists();
        
        if (!exists) {
            log.warn("文件不存在：{} (解析后路径：{})", filePath, resolvedPath);
        }
        
        return exists;
    }

    /**
     * 获取文件的绝对路径（如果文件存在）
     * 
     * @param filePath 文件路径
     * @return 绝对路径，如果文件不存在则返回null
     */
    public String getAbsolutePathIfExists(String filePath) {
        String resolvedPath = resolveFilePath(filePath);
        if (resolvedPath != null && fileExists(filePath)) {
            return resolvedPath;
        }
        return null;
    }

    /**
     * 尝试多种可能的路径来查找文件
     *
     * @param filePath 原始文件路径
     * @return 找到的文件绝对路径，如果都找不到则返回null
     */
    public String findFileWithMultiplePaths(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }

        log.debug("开始查找文件：{}", filePath);

        // 1. 如果已经是绝对路径，直接检查
        if (isAbsolutePath(filePath)) {
            if (new File(filePath).exists()) {
                log.debug("文件找到（绝对路径）：{}", filePath);
                return filePath;
            }
        }

        // 2. 尝试原始路径解析
        String resolvedPath = resolveFilePath(filePath);
        if (resolvedPath != null && new File(resolvedPath).exists()) {
            log.debug("文件找到（原始路径解析）：{}", resolvedPath);
            return resolvedPath;
        }

        // 3. 尝试天气模块配置的基础路径
        String weatherBaseDir = getWeatherBaseDir();
        String weatherPath = weatherBaseDir + filePath;
        if (new File(weatherPath).exists()) {
            log.debug("文件找到（天气基础路径）：{}", weatherPath);
            return weatherPath;
        }

        // 4. 尝试天气模块各子目录
        String[] subDirs = {"micaps/", "observation/", "data/", "precipitation-area/"};
        for (String subDir : subDirs) {
            String subDirPath = weatherBaseDir + subDir + filePath;
            if (new File(subDirPath).exists()) {
                log.debug("文件找到（天气子目录 {}）：{}", subDir, subDirPath);
                return subDirPath;
            }
        }

        // 5. 如果是相对路径，尝试通用上传目录
        if (!isAbsolutePath(filePath)) {
            if (uploadConfig != null && StringUtils.hasText(uploadConfig.getDir())) {
                String generalPath = uploadConfig.getDir() + filePath;
                if (new File(generalPath).exists()) {
                    log.debug("文件找到（通用上传目录）：{}", generalPath);
                    return generalPath;
                }
            }

            // 6. 尝试当前工作目录
            String workingDirPath = System.getProperty("user.dir") + "/" + filePath;
            if (new File(workingDirPath).exists()) {
                log.debug("文件找到（工作目录）：{}", workingDirPath);
                return workingDirPath;
            }
        }

        log.warn("文件未找到，尝试了多种路径：{}", filePath);
        return null;
    }

    /**
     * 获取天气文件的基础目录
     */
    public String getWeatherBaseDir() {
        if (weatherUploadConfig != null && StringUtils.hasText(weatherUploadConfig.getBaseDir())) {
            return weatherUploadConfig.getBaseDir();
        }
        // 如果配置不可用，使用通用上传配置
        if (uploadConfig != null && StringUtils.hasText(uploadConfig.getDir())) {
            return uploadConfig.getDir() + "weather/";
        }
        // 最后的默认值
        return "/data/upload/weather/";
    }

    /**
     * 获取指定类型文件的目录
     */
    public String getWeatherFileDir(String fileType) {
        if (weatherUploadConfig != null) {
            switch (fileType.toLowerCase()) {
                case "micaps":
                    return weatherUploadConfig.getMicapsDir();
                case "observation":
                    return weatherUploadConfig.getObservationDir();
                case "data":
                    return weatherUploadConfig.getDataDir();
                case "precipitation-area":
                    return weatherUploadConfig.getPrecipitationAreaDir();
                default:
                    return weatherUploadConfig.getBaseDir();
            }
        }

        // 如果配置不可用，使用基础目录拼接
        String baseDir = getWeatherBaseDir();
        switch (fileType.toLowerCase()) {
            case "micaps":
                return baseDir + "micaps/";
            case "observation":
                return baseDir + "observation/";
            case "data":
                return baseDir + "data/";
            case "precipitation-area":
                return baseDir + "precipitation-area/";
            default:
                return baseDir;
        }
    }
}
