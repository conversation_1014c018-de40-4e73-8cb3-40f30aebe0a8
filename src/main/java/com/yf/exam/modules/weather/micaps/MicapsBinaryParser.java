package com.yf.exam.modules.weather.micaps;

import com.yf.exam.modules.weather.util.WeatherFilePathUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.List;

/**
 * MICAPS二进制数据解析器
 * 用于解析二进制格式的MICAPS文件
 *
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Slf4j
public class MicapsBinaryParser {

    /**
     * 尝试解析二进制MICAPS文件
     *
     * @param filePath 文件路径
     * @return 解析结果对象
     * @throws IOException 文件读取异常
     */
    public static MicapsData parseBinaryMicapsFile(String filePath) throws IOException {
        // 使用路径解析工具来查找文件
        String resolvedPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
        if (resolvedPath == null) {
            throw new FileNotFoundException("MICAPS文件不存在: " + filePath);
        }

        log.debug("解析二进制MICAPS文件：{} -> {}", filePath, resolvedPath);
        File file = new File(resolvedPath);

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int bytesRead = fis.read(buffer);

            if (bytesRead < 8) {
                throw new IllegalArgumentException("文件太小，不是有效的MICAPS二进制文件");
            }

            // 尝试检测文件头中的"diamond"标识
            String headerStr = new String(buffer, 0, Math.min(bytesRead, 100));
            if (headerStr.contains("diamond")) {
                // 如果包含diamond标识，可能是文本格式，使用文本解析器
                return MicapsParser.parseMicapsFile(filePath);
            }

            // 尝试解析为二进制格式
            return parseBinaryData(file);
        }
    }

    /**
     * 解析二进制数据
     *
     * @param file 文件对象
     * @return 解析结果
     * @throws IOException 文件读取异常
     */
    private static MicapsData parseBinaryData(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] fileData = fis.readAllBytes();
            ByteBuffer buffer = ByteBuffer.wrap(fileData);

            // 尝试不同的字节序
            MicapsData result = tryParseBinary(buffer, ByteOrder.LITTLE_ENDIAN);
            if (result == null) {
                buffer.rewind();
                result = tryParseBinary(buffer, ByteOrder.BIG_ENDIAN);
            }

            if (result == null) {
                throw new IllegalArgumentException("无法解析二进制MICAPS文件格式");
            }

            return result;
        }
    }

    /**
     * 尝试以指定字节序解析二进制数据
     *
     * @param buffer 数据缓冲区
     * @param byteOrder 字节序
     * @return 解析结果，失败返回null
     */
    private static MicapsData tryParseBinary(ByteBuffer buffer, ByteOrder byteOrder) {
        buffer.order(byteOrder);

        try {
            // 尝试读取可能的文件头信息
            if (buffer.remaining() < 20) {
                return null;
            }

            // 读取前几个整数，尝试识别数据类型
            int possibleType = buffer.getInt();

            // MICAPS数据类型通常在1-20之间
            if (possibleType < 1 || possibleType > 20) {
                return null;
            }

            buffer.rewind();

            // 根据可能的数据类型尝试解析
            switch (possibleType) {
                case 1:
                    return tryParseBinaryType1(buffer);
                case 4:
                    return tryParseBinaryType4(buffer);
                default:
                    return null;
            }

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 尝试解析二进制第一类数据
     *
     * @param buffer 数据缓冲区
     * @return 第一类数据对象，失败返回null
     */
    private static MicapsType1Data tryParseBinaryType1(ByteBuffer buffer) {
        try {
            MicapsType1Data data = new MicapsType1Data();

            // 读取文件头
            int dataType = buffer.getInt();
            if (dataType != 1) {
                return null;
            }

            data.setDataType(dataType);

            // 读取时间信息
            data.setYear(buffer.getInt());
            data.setMonth(buffer.getInt());
            data.setDay(buffer.getInt());
            data.setHour(buffer.getInt());

            // 验证时间的合理性
            if (data.getYear() < 1900 || data.getYear() > 2100 ||
                data.getMonth() < 1 || data.getMonth() > 12 ||
                data.getDay() < 1 || data.getDay() > 31 ||
                data.getHour() < 0 || data.getHour() > 23) {
                return null;
            }

            // 读取站点数
            int totalStations = buffer.getInt();
            if (totalStations < 0 || totalStations > 100000) {
                return null;
            }

            data.setTotalStations(totalStations);

            // 读取站点数据
            List<MicapsStation> stations = new ArrayList<>();
            for (int i = 0; i < totalStations && buffer.remaining() >= 80; i++) {
                MicapsStation station = new MicapsStation();

                station.setStationId(buffer.getLong());
                station.setLongitude(buffer.getDouble());
                station.setLatitude(buffer.getDouble());
                station.setElevation(buffer.getDouble());
                station.setLevel(buffer.getInt());

                // 读取气象要素
                station.setTotalCloudCover(readIntOrNull(buffer));
                station.setWindDirection(readIntOrNull(buffer));
                station.setWindSpeed(readIntOrNull(buffer));
                station.setPressure(readDoubleOrNull(buffer));
                station.setPressureChange3h(readDoubleOrNull(buffer));
                station.setPastWeather1(readIntOrNull(buffer));
                station.setPastWeather2(readIntOrNull(buffer));
                station.setPrecipitation6h(readDoubleOrNull(buffer));
                station.setLowCloudType(readIntOrNull(buffer));
                station.setLowCloudAmount(readIntOrNull(buffer));
                station.setLowCloudHeight(readDoubleOrNull(buffer));
                station.setDewPoint(readDoubleOrNull(buffer));
                station.setVisibility(readDoubleOrNull(buffer));
                station.setPresentWeather(readIntOrNull(buffer));
                station.setTemperature(readDoubleOrNull(buffer));

                stations.add(station);
            }

            data.setStations(stations);
            return data;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 尝试解析二进制第四类数据
     *
     * @param buffer 数据缓冲区
     * @return 第四类数据对象，失败返回null
     */
    private static MicapsType4Data tryParseBinaryType4(ByteBuffer buffer) {
        try {
            MicapsType4Data data = new MicapsType4Data();

            // 读取文件头
            int dataType = buffer.getInt();
            if (dataType != 4) {
                return null;
            }

            data.setDataType(dataType);

            // 读取时间信息
            data.setYear(buffer.getInt());
            data.setMonth(buffer.getInt());
            data.setDay(buffer.getInt());
            data.setHour(buffer.getInt());
            data.setForecastHour(buffer.getInt());
            data.setLevel(buffer.getInt());

            // 验证时间的合理性
            if (data.getYear() < 1900 || data.getYear() > 2100 ||
                data.getMonth() < 1 || data.getMonth() > 12 ||
                data.getDay() < 1 || data.getDay() > 31 ||
                data.getHour() < 0 || data.getHour() > 23) {
                return null;
            }

            // 读取网格参数
            data.setLonInterval(buffer.getDouble());
            data.setLatInterval(buffer.getDouble());
            data.setStartLon(buffer.getDouble());
            data.setEndLon(buffer.getDouble());
            data.setStartLat(buffer.getDouble());
            data.setEndLat(buffer.getDouble());
            data.setLatGridNum(buffer.getInt());
            data.setLonGridNum(buffer.getInt());

            // 验证网格参数的合理性
            if (data.getLatGridNum() <= 0 || data.getLatGridNum() > 10000 ||
                data.getLonGridNum() <= 0 || data.getLonGridNum() > 10000) {
                return null;
            }

            // 读取等值线参数
            data.setContourInterval(buffer.getDouble());
            data.setContourStartValue(buffer.getDouble());
            data.setContourEndValue(buffer.getDouble());
            data.setSmoothFactor(buffer.getDouble());
            data.setBoldLineValue(buffer.getDouble());

            // 读取格点数据
            int totalGridPoints = data.getLatGridNum() * data.getLonGridNum();
            List<Double> gridValues = new ArrayList<>();

            for (int i = 0; i < totalGridPoints && buffer.remaining() >= 8; i++) {
                gridValues.add(buffer.getDouble());
            }

            data.setGridValues(gridValues);
            return data;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 读取整数值，9999表示缺值
     */
    private static Integer readIntOrNull(ByteBuffer buffer) {
        int value = buffer.getInt();
        return value == 9999 ? null : value;
    }

    /**
     * 读取浮点数值，9999表示缺值
     */
    private static Double readDoubleOrNull(ByteBuffer buffer) {
        double value = buffer.getDouble();
        return value == 9999.0 ? null : value;
    }
}
