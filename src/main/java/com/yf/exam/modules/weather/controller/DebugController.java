package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.micaps.MicapsDataService;
import com.yf.exam.modules.weather.util.WeatherFilePathUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.*;

/**
 * 调试控制器
 * 用于调试文件路径和数据解析问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/debug")
@Api(tags = "调试接口")
public class DebugController extends BaseController {

    @Autowired(required = false)
    private MicapsDataService micapsDataService;

    /**
     * 测试文件路径解析
     */
    @ApiOperation(value = "测试文件路径解析")
    @PostMapping("/test-path")
    public ApiRest<Map<String, Object>> testPath(@RequestParam("filePath") String filePath) {
        Map<String, Object> result = new HashMap<>();
        List<String> logs = new ArrayList<>();

        try {
            logs.add("开始测试文件路径: " + filePath);

            // 1. 使用工具类查找文件
            String foundPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
            if (foundPath != null) {
                logs.add("✅ 工具类找到文件: " + foundPath);
                
                // 检查文件信息
                File file = new File(foundPath);
                logs.add("文件大小: " + file.length() + " bytes");
                logs.add("可读: " + file.canRead());
                logs.add("最后修改: " + new Date(file.lastModified()));
                
                result.put("success", true);
                result.put("foundPath", foundPath);
            } else {
                logs.add("❌ 工具类未找到文件");
                result.put("success", false);
            }

            // 2. 尝试直接路径
            File directFile = new File(filePath);
            logs.add("直接路径存在: " + directFile.exists());

            // 3. 尝试解析路径
            String resolvedPath = WeatherFilePathUtil.resolveFilePath(filePath);
            logs.add("解析后路径: " + resolvedPath);
            if (resolvedPath != null) {
                File resolvedFile = new File(resolvedPath);
                logs.add("解析路径存在: " + resolvedFile.exists());
            }

            result.put("logs", logs);
            return super.success(result);

        } catch (Exception e) {
            log.error("测试文件路径时出错", e);
            logs.add("❌ 测试过程中出错: " + e.getMessage());
            result.put("success", false);
            result.put("logs", logs);
            return super.success(result);
        }
    }

    /**
     * 测试MICAPS文件解析
     */
    @ApiOperation(value = "测试MICAPS文件解析")
    @PostMapping("/test-micaps")
    public ApiRest<Map<String, Object>> testMicaps(@RequestParam("filePath") String filePath) {
        Map<String, Object> result = new HashMap<>();
        List<String> logs = new ArrayList<>();

        try {
            logs.add("开始测试MICAPS文件解析: " + filePath);

            if (micapsDataService == null) {
                logs.add("❌ MicapsDataService未注入");
                result.put("success", false);
                result.put("logs", logs);
                return super.success(result);
            }

            // 1. 先检查文件是否存在
            String foundPath = WeatherFilePathUtil.findFileWithMultiplePaths(filePath);
            if (foundPath == null) {
                logs.add("❌ 文件不存在: " + filePath);
                result.put("success", false);
                result.put("logs", logs);
                return super.success(result);
            }

            logs.add("✅ 文件找到: " + foundPath);

            // 2. 尝试解析文件
            try {
                Object micapsData = micapsDataService.parseMicapsFile(filePath);
                if (micapsData != null) {
                    logs.add("✅ MICAPS文件解析成功");
                    logs.add("数据类型: " + micapsData.getClass().getSimpleName());
                    result.put("success", true);
                    result.put("dataType", micapsData.getClass().getSimpleName());
                } else {
                    logs.add("❌ MICAPS文件解析返回null");
                    result.put("success", false);
                }
            } catch (Exception parseException) {
                logs.add("❌ MICAPS文件解析失败: " + parseException.getMessage());
                result.put("success", false);
                result.put("parseError", parseException.getMessage());
            }

            result.put("logs", logs);
            return super.success(result);

        } catch (Exception e) {
            log.error("测试MICAPS文件解析时出错", e);
            logs.add("❌ 测试过程中出错: " + e.getMessage());
            result.put("success", false);
            result.put("logs", logs);
            return super.success(result);
        }
    }

    /**
     * 获取系统信息
     */
    @ApiOperation(value = "获取系统信息")
    @GetMapping("/system-info")
    public ApiRest<Map<String, Object>> getSystemInfo() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 系统属性
            Map<String, String> systemProps = new HashMap<>();
            systemProps.put("os.name", System.getProperty("os.name"));
            systemProps.put("os.version", System.getProperty("os.version"));
            systemProps.put("user.name", System.getProperty("user.name"));
            systemProps.put("user.dir", System.getProperty("user.dir"));
            systemProps.put("java.version", System.getProperty("java.version"));
            systemProps.put("file.separator", System.getProperty("file.separator"));

            // 目录信息
            Map<String, Object> dirInfo = new HashMap<>();
            String baseDir = "/data/upload/weather/";
            File baseDirFile = new File(baseDir);
            dirInfo.put("baseDir", baseDir);
            dirInfo.put("exists", baseDirFile.exists());
            dirInfo.put("canRead", baseDirFile.canRead());
            dirInfo.put("canWrite", baseDirFile.canWrite());

            // 子目录信息
            Map<String, Object> subDirs = new HashMap<>();
            String[] subDirNames = {"micaps", "observation", "data", "precipitation-area"};
            for (String subDirName : subDirNames) {
                File subDir = new File(baseDir + subDirName);
                Map<String, Object> subDirInfo = new HashMap<>();
                subDirInfo.put("exists", subDir.exists());
                subDirInfo.put("canRead", subDir.canRead());
                subDirInfo.put("canWrite", subDir.canWrite());
                if (subDir.exists()) {
                    File[] files = subDir.listFiles();
                    subDirInfo.put("fileCount", files != null ? files.length : 0);
                }
                subDirs.put(subDirName, subDirInfo);
            }

            result.put("systemProperties", systemProps);
            result.put("directoryInfo", dirInfo);
            result.put("subDirectories", subDirs);
            result.put("success", true);

            return super.success(result);

        } catch (Exception e) {
            log.error("获取系统信息时出错", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return super.success(result);
        }
    }

    /**
     * 模拟判卷过程
     */
    @ApiOperation(value = "模拟判卷过程")
    @PostMapping("/simulate-grading")
    public ApiRest<Map<String, Object>> simulateGrading(
            @RequestParam("actualFilePath") String actualFilePath,
            @RequestParam("cmaMesoFilePath") String cmaMesoFilePath) {
        
        Map<String, Object> result = new HashMap<>();
        List<String> logs = new ArrayList<>();

        try {
            logs.add("开始模拟判卷过程...");
            logs.add("实况文件路径: " + actualFilePath);
            logs.add("CMA-MESO文件路径: " + cmaMesoFilePath);

            // 1. 检查实况文件
            String actualFoundPath = WeatherFilePathUtil.findFileWithMultiplePaths(actualFilePath);
            if (actualFoundPath != null) {
                logs.add("✅ 实况文件找到: " + actualFoundPath);
            } else {
                logs.add("❌ 实况文件未找到: " + actualFilePath);
                result.put("success", false);
                result.put("logs", logs);
                return super.success(result);
            }

            // 2. 检查CMA-MESO文件
            String cmaMesoFoundPath = WeatherFilePathUtil.findFileWithMultiplePaths(cmaMesoFilePath);
            if (cmaMesoFoundPath != null) {
                logs.add("✅ CMA-MESO文件找到: " + cmaMesoFoundPath);
            } else {
                logs.add("❌ CMA-MESO文件未找到: " + cmaMesoFilePath);
                result.put("success", false);
                result.put("logs", logs);
                return super.success(result);
            }

            // 3. 尝试解析文件
            if (micapsDataService != null) {
                try {
                    Object actualData = micapsDataService.parseMicapsFile(actualFilePath);
                    logs.add("✅ 实况文件解析成功: " + actualData.getClass().getSimpleName());
                } catch (Exception e) {
                    logs.add("❌ 实况文件解析失败: " + e.getMessage());
                }

                try {
                    Object cmaMesoData = micapsDataService.parseMicapsFile(cmaMesoFilePath);
                    logs.add("✅ CMA-MESO文件解析成功: " + cmaMesoData.getClass().getSimpleName());
                } catch (Exception e) {
                    logs.add("❌ CMA-MESO文件解析失败: " + e.getMessage());
                }
            } else {
                logs.add("❌ MicapsDataService未注入，无法解析文件");
            }

            result.put("success", true);
            result.put("logs", logs);
            return super.success(result);

        } catch (Exception e) {
            log.error("模拟判卷过程时出错", e);
            logs.add("❌ 模拟过程中出错: " + e.getMessage());
            result.put("success", false);
            result.put("logs", logs);
            return super.success(result);
        }
    }
}
