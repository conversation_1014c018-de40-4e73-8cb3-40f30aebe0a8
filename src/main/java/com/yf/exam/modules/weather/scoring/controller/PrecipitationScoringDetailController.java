package com.yf.exam.modules.weather.scoring.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.weather.scoring.dto.StationScoringDetail;
import com.yf.exam.modules.weather.scoring.dto.LevelTSScoringDetail;
import com.yf.exam.modules.weather.scoring.service.PrecipitationAreaScoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 降水落区评分详情控制器
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Api(tags = "降水落区评分详情")
@RestController
@RequestMapping("/weather/scoring/precipitation/detail")
@Slf4j
public class PrecipitationScoringDetailController extends BaseController {

    @Autowired
    private PrecipitationAreaScoringService precipitationAreaScoringService;

    @ApiOperation("获取降水落区评分详细信息")
    @PostMapping("/calculate")
    public ApiRest<Map<String, Object>> calculateDetailedScore(
            @ApiParam("实况降水文件路径") @RequestParam String actualFilePath,
            @ApiParam("CMA-MESO文件路径") @RequestParam String cmaMesoFilePath,
            @ApiParam("考生答案JSON") @RequestBody Map<String, Object> studentAnswer,
            @ApiParam("区域编码（可选）") @RequestParam(required = false) String regionCode) {
        
        try {
            // 计算评分
            PrecipitationScoringResult result = precipitationAreaScoringService
                .calculatePrecipitationScore(actualFilePath, cmaMesoFilePath, studentAnswer, regionCode);
            
            if (!result.isSuccess()) {
                return failure(result.getMessage());
            }
            
            // 构建详细信息响应
            Map<String, Object> detailedResult = new HashMap<>();
            
            // 基本评分信息
            detailedResult.put("finalScore", result.getFinalScore());
            detailedResult.put("totalStations", result.getTotalStations());
            detailedResult.put("success", result.isSuccess());
            detailedResult.put("message", result.getMessage());
            
            // TS评分信息
            detailedResult.put("studentTSScores", result.getStudentTSScores());
            detailedResult.put("cmaMesoTSScores", result.getCmaMesoTSScores());
            detailedResult.put("baseScores", result.getBaseScores());
            detailedResult.put("skillScores", result.getSkillScores());
            detailedResult.put("weights", result.getWeights());
            
            // 详细评分信息
            detailedResult.put("stationDetails", result.getStationDetails());
            detailedResult.put("levelTSDetails", result.getLevelTSDetails());
            
            // 统计摘要
            detailedResult.put("scoringSummary", generateScoringSummary(result));
            
            return success(detailedResult);
            
        } catch (Exception e) {
            log.error("计算降水落区评分详情失败", e);
            return failure("计算评分失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取指定站点的详细评分信息")
    @GetMapping("/station/{stationId}")
    public ApiRest<StationScoringDetail> getStationDetail(
            @ApiParam("站点ID") @PathVariable Long stationId,
            @ApiParam("评分结果ID或缓存键") @RequestParam String resultKey) {
        
        try {
            // 这里可以从缓存或数据库中获取评分结果
            // 暂时返回示例数据
            return failure("功能开发中，请使用完整评分接口");
            
        } catch (Exception e) {
            log.error("获取站点详细信息失败", e);
            return failure("获取站点详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取指定量级的TS评分详情")
    @GetMapping("/level/{level}")
    public ApiRest<LevelTSScoringDetail> getLevelTSDetail(
            @ApiParam("降水量级") @PathVariable String level,
            @ApiParam("评分结果ID或缓存键") @RequestParam String resultKey) {
        
        try {
            // 这里可以从缓存或数据库中获取评分结果
            // 暂时返回示例数据
            return failure("功能开发中，请使用完整评分接口");
            
        } catch (Exception e) {
            log.error("获取量级TS详情失败", e);
            return failure("获取量级TS详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("导出站点详细评分信息为CSV")
    @PostMapping("/export/stations")
    public ApiRest<String> exportStationDetails(
            @ApiParam("实况降水文件路径") @RequestParam String actualFilePath,
            @ApiParam("CMA-MESO文件路径") @RequestParam String cmaMesoFilePath,
            @ApiParam("考生答案JSON") @RequestBody Map<String, Object> studentAnswer,
            @ApiParam("区域编码（可选）") @RequestParam(required = false) String regionCode) {
        
        try {
            // 计算评分
            PrecipitationScoringResult result = precipitationAreaScoringService
                .calculatePrecipitationScore(actualFilePath, cmaMesoFilePath, studentAnswer, regionCode);
            
            if (!result.isSuccess()) {
                return failure(result.getMessage());
            }
            
            // 生成CSV内容
            String csvContent = generateStationDetailsCSV(result.getStationDetails());
            
            return success(csvContent);
            
        } catch (Exception e) {
            log.error("导出站点详情失败", e);
            return failure("导出失败：" + e.getMessage());
        }
    }

    /**
     * 生成评分摘要
     */
    private String generateScoringSummary(PrecipitationScoringResult result) {
        StringBuilder summary = new StringBuilder();
        summary.append("=== 降水落区评分详细摘要 ===\n\n");
        
        summary.append(String.format("最终得分：%.2f分（满分40分）\n", result.getFinalScore()));
        summary.append(String.format("总站点数：%d个\n\n", result.getTotalStations()));
        
        // 各量级TS评分摘要
        summary.append("=== 各量级TS评分对比 ===\n");
        summary.append(String.format("%-10s %-10s %-10s %-10s %-10s %-10s %-10s\n", 
            "量级", "学生TS", "CMA-TS", "基础分", "技巧分", "权重", "贡献"));
        summary.append("----------------------------------------------------------------\n");
        
        if (result.getLevelTSDetails() != null) {
            for (LevelTSScoringDetail detail : result.getLevelTSDetails()) {
                summary.append(String.format("%-10s %-10.3f %-10.3f %-10.1f %-10.3f %-10.2f %-10.2f\n",
                    detail.getLevel(),
                    detail.getStudentTSStats() != null ? detail.getStudentTSStats().getTsScore() : 0.0,
                    detail.getCmaMesoTSStats() != null ? detail.getCmaMesoTSStats().getTsScore() : 0.0,
                    detail.getStudentBaseScore() != null ? detail.getStudentBaseScore() : 0.0,
                    detail.getStudentSkillScore() != null ? detail.getStudentSkillScore() : 0.0,
                    detail.getWeight() != null ? detail.getWeight() : 0.0,
                    detail.getContributionToFinalScore() != null ? detail.getContributionToFinalScore() : 0.0));
            }
        }
        
        // 站点统计摘要
        if (result.getStationDetails() != null) {
            summary.append("\n=== 站点统计摘要 ===\n");
            Map<String, Long> actualLevelCount = result.getStationDetails().stream()
                .collect(Collectors.groupingBy(StationScoringDetail::getActualLevel, Collectors.counting()));
            
            summary.append("实况降水量级分布：\n");
            actualLevelCount.forEach((level, count) -> 
                summary.append(String.format("  %s：%d个站点\n", level, count)));
        }
        
        return summary.toString();
    }

    /**
     * 生成站点详情CSV内容
     */
    private String generateStationDetailsCSV(List<StationScoringDetail> stationDetails) {
        StringBuilder csv = new StringBuilder();
        
        // CSV头部
        csv.append("站点ID,经度,纬度,实况降水量,实况等级,考生预报降水量,考生预报等级,CMA-MESO预报降水量,CMA-MESO预报等级,");
        csv.append("晴雨-考生结果,晴雨-CMA结果,量级-考生结果,量级-CMA结果,特殊规则说明\n");
        
        // 数据行
        for (StationScoringDetail detail : stationDetails) {
            csv.append(String.format("%d,%.4f,%.4f,%.1f,%s,%.1f,%s,%.1f,%s,",
                detail.getStationId(),
                detail.getLongitude(),
                detail.getLatitude(),
                detail.getActualPrecipitation(),
                detail.getActualLevel(),
                detail.getStudentForecastPrecipitation(),
                detail.getStudentForecastLevel(),
                detail.getCmaMesoForecastPrecipitation(),
                detail.getCmaMesoForecastLevel()));
            
            // 晴雨评分结果
            if (detail.getRainNoRainDetail() != null) {
                csv.append(detail.getRainNoRainDetail().getStudentResultType()).append(",");
                csv.append(detail.getRainNoRainDetail().getCmaMesoResultType()).append(",");
            } else {
                csv.append(",,");
            }
            
            // 量级评分结果
            if (detail.getLevelDetail() != null) {
                csv.append(detail.getLevelDetail().getStudentResultType()).append(",");
                csv.append(detail.getLevelDetail().getCmaMesoResultType()).append(",");
                csv.append(detail.getLevelDetail().getSpecialRuleNote() != null ? 
                    detail.getLevelDetail().getSpecialRuleNote() : "");
            } else {
                csv.append(",,");
            }
            
            csv.append("\n");
        }
        
        return csv.toString();
    }
}
