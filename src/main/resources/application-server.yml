# 生产环境配置文件
spring:
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: exam
    password: Exam123!@#
    # druid相关配置
    druid:
      max-active: 50
      initial-size: 5
      min-idle: 5
      async-init: true
      # 监控统计
      filters: stat,wall
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 5000
        wall:
          config:
            create-table-allow: false
            alter-table-allow: false
            drop-table-allow: false
            truncate-allow: false

  # Redis配置
  redis:
    host: *************
    port: 6379
    database: 0
    password: hebj
    timeout: 10s
    ssl.enabled: false
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.yf.exam.modules.*.entity
  type-handlers-package: com.yf.exam.config
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

  # 定时任务配置
  quartz:
    # 数据库方式
    job-store-type: jdbc
    # quartz 相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: eamScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            clusterCheckinInterval: 10000
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true

# 开启文档
swagger:
  enable: true

logging:
  level:
    root: info
    # 抑制Apache Commons BeanUtils的DEBUG日志
    org.apache.commons.beanutils: warn
    # 抑制Dozer的DEBUG日志
    org.dozer: warn
    # 抑制Druid连接池的DEBUG日志
    com.alibaba.druid: warn
    # 保持应用业务日志为INFO
    com.yf.exam: info
  path: logs/${spring.application.name}/

conf:
  upload:
    # 物理文件存储位置，以/结束，windows已正斜杠，如：d:/exam-upload/
    dir: /data/upload/
    # 访问地址，注意不要去除/upload/file/，此节点为虚拟标识符
    # 如：http://localhost:8101/upload/file/exam.jpg，对应物理文件为：/data/upload/exam.jpg
    url: http://localhost:8101/upload/file/
    # 允许上传的文件后缀
    allow-extensions: jpg,jpeg,png,dat,txt,nc,grib,grib2,zip,rar,7z,tar,gz,bz2,cma,***************
  # 天气模块文件上传配置
  weather:
    # 天气文件存储基础路径，以/结束
    base-dir: /data/upload/weather/
