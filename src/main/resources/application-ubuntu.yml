spring:
  profiles:
    active: ubuntu
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      idle-timeout: 600000
      maximum-pool-size: 10
      auto-commit: true
      pool-name: MyHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

# 文件上传配置 - Ubuntu环境
conf:
  upload:
    # Ubuntu物理文件存储位置，以/结束
    dir: /data/upload/
    # 访问地址，注意不要去除/upload/file/，此节点为虚拟标识符
    url: http://localhost:8101/upload/file/
    # 允许上传的文件后缀
    allow-extensions: jpg,jpeg,png,dat,txt,nc,grib,grib2,zip,rar,7z,tar,gz,bz2,cma,000,024,036,048
  # 天气模块文件上传配置 - Ubuntu环境
  weather:
    # 天气文件存储基础路径，以/结束
    base-dir: /data/upload/weather/

# 开启文档
swagger:
  enable: true

# 日志配置 - Ubuntu环境
logging:
  level:
    root: info
    com.yf.exam.modules.weather: debug
  path: /var/log/exam/
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 服务器配置
server:
  port: 8101
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10
