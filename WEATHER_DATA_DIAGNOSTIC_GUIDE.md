# 天气数据诊断指南

## 问题描述
`WeatherScoringEngine.extractPredictedData()` 方法在开发环境正常工作，但在生产环境返回 null 的问题。

## 🔍 最新发现
根据最新的诊断日志，我们发现：
1. **数据库中确实有数据**，JSON格式正确
2. **问题出现在 MyBatis TypeHandler 层面**
3. `CustomJacksonTypeHandler` 可能没有被正确调用或返回了 null

## 🚀 最新解决方案
我们已经实现了一个**临时绕过方案**：
- 当 TypeHandler 返回 null 时，直接使用 JdbcTemplate 查询数据库
- 手动进行 JSON 反序列化
- 确保评分功能能够正常工作

## 解决方案概述

### 1. 增强日志记录
- 修改 `extractPredictedData` 方法，添加详细的 `info` 级别日志
- 修改 `CustomJacksonTypeHandler`，使用 `info` 级别记录关键信息
- 添加自动诊断机制，当数据为空时触发详细诊断

### 2. 创建诊断工具
- `WeatherDataDiagnosticUtil`：直接查询数据库原始数据的工具类
- `WeatherDataDiagnosticController`：提供 REST API 接口进行诊断

### 3. 自动诊断机制
- 当 `extractPredictedData` 返回空数据时，自动触发诊断流程
- 提供详细的问题分析和修复建议

## 使用方法

### 1. 部署更新
将修改后的代码部署到生产环境。

### 2. 诊断接口

#### 2.1 诊断特定答案
```http
GET /exam/api/weather/diagnostic/answer/{answerId}
```

**示例**：
```bash
curl -X GET "http://your-server/exam/api/weather/diagnostic/answer/1234567890"
```

#### 2.2 批量诊断最近记录
```http
GET /exam/api/weather/diagnostic/recent?limit=10
```

**示例**：
```bash
curl -X GET "http://your-server/exam/api/weather/diagnostic/recent?limit=5"
```

#### 2.3 获取诊断建议
```http
GET /exam/api/weather/diagnostic/suggestions
```

#### 2.4 测试方法
```http
GET /exam/api/weather/diagnostic/test-extract/{answerId}
```

### 3. 查看日志
部署后，当执行评分功能时，会在日志中看到详细的诊断信息：

```log
2024-12-19 10:30:00 [main] INFO  WeatherScoringEngine - === 开始提取预测数据 ===
2024-12-19 10:30:00 [main] INFO  WeatherScoringEngine - 答案ID: 1234567890
2024-12-19 10:30:00 [main] INFO  WeatherScoringEngine - 用户ID: user123
2024-12-19 10:30:00 [main] INFO  WeatherScoringEngine - 题目ID: question456
2024-12-19 10:30:00 [main] INFO  CustomJacksonTypeHandler - === CustomJacksonTypeHandler 读取数据 (precipitation_answer) ===
2024-12-19 10:30:00 [main] INFO  CustomJacksonTypeHandler - 原始JSON长度: 0
2024-12-19 10:30:00 [main] WARN  CustomJacksonTypeHandler - JSON为空或null，source: precipitation_answer, json: [null], 返回null
```

### 4. 手动数据库检查
如果需要手动检查数据库，可以执行以下 SQL：

```sql
-- 检查特定答案记录
SELECT 
    id, 
    precipitation_answer, 
    weather_answer, 
    answer_status,
    LENGTH(precipitation_answer) as p_len,
    LENGTH(weather_answer) as w_len,
    create_time,
    update_time
FROM el_weather_history_exam_answer 
WHERE id = 'YOUR_ANSWER_ID';

-- 批量检查最近记录
SELECT 
    id,
    precipitation_answer IS NULL as p_null,
    weather_answer IS NULL as w_null,
    LENGTH(precipitation_answer) as p_len,
    LENGTH(weather_answer) as w_len,
    answer_status,
    create_time
FROM el_weather_history_exam_answer 
ORDER BY create_time DESC 
LIMIT 10;
```

## 可能的问题原因

### 1. 数据库层面
- JSON 字段为 NULL
- JSON 字段为空字符串
- 数据库字符编码问题
- 用户未提交答案（answer_status != 1）

### 2. 应用层面
- `CustomJacksonTypeHandler` 反序列化失败
- JSON 格式无效
- 字符编码转换问题

### 3. 环境差异
- 开发环境和生产环境的数据库配置不同
- 日志级别配置不同
- 字符编码设置不同

## 诊断流程

1. **执行评分功能**，触发 `extractPredictedData` 方法
2. **查看日志输出**，确认是否有详细的诊断信息
3. **调用诊断接口**，获取数据库原始数据分析
4. **根据诊断结果**，确定问题所在：
   - 如果数据库中数据为 NULL → 检查数据保存逻辑
   - 如果数据库中有数据但反序列化失败 → 检查 JSON 格式和字符编码
   - 如果部分字段有数据 → 检查具体的业务逻辑

## 修复建议

### 1. 数据为 NULL 的情况
- 检查前端数据提交逻辑
- 检查后端数据保存逻辑
- 确认用户是否真的提交了答案

### 2. JSON 反序列化失败
- 检查数据库字符编码设置
- 验证 JSON 格式是否正确
- 检查 `CustomJacksonTypeHandler` 的配置

### 3. 环境配置问题
- 对比开发环境和生产环境的配置差异
- 检查数据库连接字符串
- 确认日志级别配置

## 注意事项

1. **日志级别**：现在使用 `info` 级别记录关键信息，确保生产环境能看到
2. **性能影响**：诊断日志可能会增加日志量，建议问题解决后适当调整
3. **数据安全**：诊断日志可能包含敏感数据，注意日志文件的安全性
4. **临时接口**：诊断接口是临时调试用的，生产环境稳定后可以考虑移除

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 完整的日志输出
2. 诊断接口的返回结果
3. 数据库查询的结果
4. 具体的答案ID和相关信息
